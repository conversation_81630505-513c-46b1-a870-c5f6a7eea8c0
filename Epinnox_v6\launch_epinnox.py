#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean launch script for the integrated trading interface
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 importsE
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    # print("✓ PyQt5 loaded successfully")
except ImportError:
    print("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# Worker thread for non-blocking LLM analysis
class LLMAnalysisWorker(QObject):
    """Worker thread for LLM analysis to prevent UI blocking"""
    analysis_complete = pyqtSignal(dict)  # Signal when analysis is done
    error_occurred = pyqtSignal(str)  # Signal when error occurs

    def __init__(self, lmstudio_runner):
        super().__init__()
        self.lmstudio_runner = lmstudio_runner

    def run_analysis(self, prompt, temperature=0.7, max_tokens=200):
        """Run LLM analysis in background thread"""
        try:
            response = self.lmstudio_runner.run_inference(
                prompt=prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Emit result
            self.analysis_complete.emit({
                'response': response,
                'success': True
            })

        except Exception as e:
            self.error_occurred.emit(str(e))

# 🚀 PERFORMANCE FIX: Background Task Worker
class BackgroundTaskWorker(QRunnable):
    """Worker for running tasks in background threads to prevent UI blocking"""

    class Signals(QObject):
        finished = pyqtSignal(str, object)  # task_name, result
        error = pyqtSignal(str, str)  # task_name, error_message

    def __init__(self, task_name, task_function):
        super().__init__()
        self.task_name = task_name
        self.task_function = task_function
        self.signals = self.Signals()

    def run(self):
        """Execute the task in background thread"""
        try:
            result = self.task_function()
            self.signals.finished.emit(self.task_name, result)
        except Exception as e:
            self.signals.error.emit(self.task_name, str(e))

# PyQtGraph for charting
try:
    import pyqtgraph as pg
    # print("✓ PyQtGraph loaded successfully")
    # Configure PyQtGraph
    pg.setConfigOptions(useOpenGL=False, antialias=True)
except ImportError:
    print("✗ PyQtGraph not installed. Install with: pip install pyqtgraph")
    sys.exit(1)

# Load production configuration
try:
    from config.production_loader import (
        get_production_config, is_production_ready, get_live_trading_credentials,
        get_demo_mode_setting, enforce_production_settings
    )

    # Get production configuration
    prod_config = get_production_config()

    # Print production readiness status
    production_ready = prod_config.print_production_status()

    # Enforce production settings
    production_settings = enforce_production_settings()
    demo_mode = production_settings['demo_mode']

    # Initialize logging system
    try:
        from core.logging_config import initialize_logging, get_logger
        initialize_logging(debug_mode=production_settings.get('debug_mode', False))
        logger = get_logger('main')
        logger.info("Epinnox v6 starting up...")
    except Exception as e:
        print(f"⚠️ Logging initialization warning: {e}")
        logger = None

    # Initialize performance monitoring
    try:
        from core.performance_monitor import initialize_performance_monitor
        perf_monitor = initialize_performance_monitor(monitoring_interval=10)
        if logger:
            logger.info("Performance monitoring initialized")
    except Exception as e:
        print(f"⚠️ Performance monitoring warning: {e}")
        perf_monitor = None

    # Perform automatic cache cleanup
    try:
        from utils.cache_manager import cleanup_cache
        cache_stats = cleanup_cache()
        if cache_stats['files_removed'] > 0:
            print(f"🧹 Cache cleanup: removed {cache_stats['files_removed']} files, "
                  f"freed {cache_stats['bytes_freed'] / 1024 / 1024:.1f} MB")
            if logger:
                logger.info(f"Cache cleanup: removed {cache_stats['files_removed']} files")
    except Exception as e:
        print(f"⚠️ Cache cleanup warning: {e}")
        if logger:
            logger.warning(f"Cache cleanup failed: {e}")

    if not production_ready:
        print("\n⚠️ WARNING: System not fully configured for production!")
        print("   Please review the issues above before live trading.")

except ImportError as e:
    print(f"⚠️ Could not load production configuration: {e}")
    production_ready = False
    demo_mode = True  # Fallback to demo mode if config fails

# Try to import ccxt for real trading functionality
try:
    import ccxt
    print("✓ CCXT library available")

    # Initialize exchange (HTX/Huobi for LIVE TRADING)
    try:
        # Load real API credentials from production config
        creds = get_live_trading_credentials()

        # Validate credentials before using them
        if not creds['apiKey'] or not creds['secret']:
            raise ValueError("Missing HTX API credentials. Please check credentials.yaml")

        print(f"🔑 Loading HTX credentials...")
        print(f"   API Key: {creds['apiKey'][:8]}...{creds['apiKey'][-4:]}")
        print(f"   Secret: {'*' * len(creds['secret'])}")
        print(f"   Sandbox: {'ON' if creds.get('sandbox', False) else 'OFF'}")

        # Initialize HTX exchange with REAL credentials for LINEAR SWAP trading
        exchange_config = {
            'apiKey': creds['apiKey'],
            'secret': creds['secret'],
            'sandbox': creds.get('sandbox', False),  # Use sandbox setting from credentials
            'enableRateLimit': True,
            'timeout': 30000,  # 30 second timeout
            'rateLimit': 200,  # Rate limit in ms (increased to avoid HTX limits)
            'options': {
                'defaultType': 'swap',  # HTX Linear Swaps
                'marginMode': 'cross',  # Cross margin mode
                'fetchCurrencies': False,  # Disable problematic endpoint
            },
            'urls': {
                'api': {
                    'swap': 'https://api.hbdm.com',  # HTX Linear Swap API
                }
            }
        }

        # Add password if provided (some exchanges require it)
        if creds.get('password'):
            exchange_config['password'] = creds['password']

        # Use the correct exchange based on credentials
        exchange_name = creds.get('exchange', 'htx').lower()
        if exchange_name == 'huobi':
            exchange_name = 'htx'  # HTX is the new name for Huobi

        if exchange_name == 'htx':
            exchange = ccxt.htx(exchange_config)
        elif exchange_name == 'huobi':
            exchange = ccxt.huobi(exchange_config)
        else:
            raise ValueError(f"Unsupported exchange: {exchange_name}")
        demo_mode = False  # LIVE TRADING MODE

        # Test the connection
        try:
            exchange.load_markets()
            print("✅ HTX exchange initialized and connected successfully")
            print(f"🔑 Using API key: {creds['apiKey'][:8]}...")  # Show partial key for verification
        except Exception as test_error:
            print(f"❌ HTX connection test failed: {test_error}")
            raise
    except Exception as e:
        print(f"⚠ Could not initialize exchange: {e}")
        exchange = None
        demo_mode = True

    # 🔧 FIX 2: Helper function for minimum order size validation
    def validate_minimum_order_size_global(symbol, amount):
        """Validate if order amount meets exchange minimum requirements"""
        try:
            # Check if exchange is available
            if not exchange or not hasattr(exchange, 'markets'):
                print(f"⚠️ Exchange markets data not available for validation")
                return True  # Allow order if we can't validate

            # Get market info for the symbol
            if symbol not in exchange.markets:
                print(f"⚠️ Symbol {symbol} not found in exchange markets")
                return True  # Allow order if symbol not found

            market = exchange.markets[symbol]
            limits = market.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min')

            if min_amount is not None and amount < min_amount:
                error_msg = f"Order amount {amount:.8f} below minimum {min_amount:.8f} for {symbol}"
                print(f"❌ {error_msg}")
                return False

            print(f"✅ Order amount {amount:.8f} meets minimum requirements for {symbol}")
            return True

        except Exception as e:
            print(f"⚠️ Error validating minimum order size: {e}")
            return True  # Allow order if validation fails

    # 🚀 ENHANCED: Intelligent limit order functions (replacing market orders)
    def place_intelligent_limit_order(symbol, side, amount, confidence=85.0, params={}):
        """🚀 NEW: Place intelligent limit order using professional scalping system"""
        try:
            # 🔧 FIX 2: Validate minimum order size before placing order
            if not validate_minimum_order_size_global(symbol, amount):
                return None

            # Get the main window instance
            main_window_instance = EpinnoxTradingInterface._instance

            # Use the intelligent limit order manager from LLM action executors
            if (main_window_instance and
                hasattr(main_window_instance, 'llm_action_executors') and
                main_window_instance.llm_action_executors and
                main_window_instance.llm_action_executors.limit_order_manager):

                smart_order = main_window_instance.llm_action_executors.limit_order_manager.place_smart_limit_order(
                    symbol=symbol,
                    side=side,
                    amount=amount,
                    confidence=confidence,
                    timeout_seconds=60  # 60 seconds for scalping
                )

                if smart_order:
                    print(f"🎯 Intelligent limit order placed: {side.upper()} {amount} {symbol} @ ${smart_order.price:.6f}")
                    return {"id": smart_order.id, "status": "pending", "price": smart_order.price}
                else:
                    print(f"❌ Failed to place intelligent limit order for {symbol}")
                    return None
            else:
                # Fallback to traditional limit order
                return place_limit_order_fallback(symbol, side, amount, params)

        except Exception as e:
            print(f"Error placing intelligent limit order: {e}")
            return place_limit_order_fallback(symbol, side, amount, params)

    def place_limit_order_fallback(symbol, side, amount, params={}):
        """Fallback limit order using ccxt when intelligent system not available"""
        # 🔧 FIX 2: Validate minimum order size before placing order
        if not validate_minimum_order_size_global(symbol, amount):
            return None

        if exchange and not demo_mode:
            try:
                # Get current market price for limit order
                ticker = exchange.fetch_ticker(symbol)
                current_price = ticker['last']

                # Place limit order at current market price (acts like market order but with limit protection)
                order = exchange.create_limit_order(symbol, side, amount, current_price, params)
                print(f"📋 Fallback limit order: {side.upper()} {amount} {symbol} @ ${current_price:.6f}")
                return order
            except Exception as e:
                print(f"Error placing fallback limit order: {e}")
                return None
        else:
            print(f"DEMO: Intelligent limit order {side.upper()} {amount} {symbol}")
            return {"id": f"smart_limit_{int(datetime.now().timestamp())}", "status": "pending"}

    def place_limit_order(symbol, side, amount, price, params={}):
        """🚀 ENHANCED: Traditional limit order (now uses intelligent system when possible)"""
        try:
            # 🔧 FIX 2: Validate minimum order size before placing order
            if not validate_minimum_order_size_global(symbol, amount):
                return None

            # Get the main window instance
            main_window_instance = EpinnoxTradingInterface._instance

            # Try to use intelligent limit order system first
            if (main_window_instance and
                hasattr(main_window_instance, 'llm_action_executors') and
                main_window_instance.llm_action_executors and
                main_window_instance.llm_action_executors.limit_order_manager):

                # Calculate confidence based on how close price is to market
                ticker = exchange.fetch_ticker(symbol) if exchange else None
                confidence = 85.0  # Default confidence

                if ticker:
                    market_price = ticker['last']
                    price_diff_pct = abs(price - market_price) / market_price * 100

                    # Adjust confidence based on price aggressiveness
                    if price_diff_pct < 0.01:  # Very close to market
                        confidence = 95.0
                    elif price_diff_pct < 0.05:  # Close to market
                        confidence = 90.0
                    elif price_diff_pct < 0.1:  # Moderate distance
                        confidence = 80.0
                    else:  # Far from market
                        confidence = 70.0

                # Use intelligent system with custom price
                smart_order = main_window_instance.llm_action_executors.limit_order_manager.place_smart_limit_order(
                    symbol=symbol,
                    side=side,
                    amount=amount,
                    confidence=confidence
                )

                if smart_order:
                    print(f"🎯 Smart limit order: {side.upper()} {amount} {symbol} @ ${smart_order.price:.6f}")
                    return {"id": smart_order.id, "status": "pending", "price": smart_order.price}

            # Fallback to traditional method
            return place_limit_order_fallback(symbol, side, amount, params)

        except Exception as e:
            print(f"Error in enhanced limit order: {e}")
            return place_limit_order_fallback(symbol, side, amount, params)

    def place_market_order(symbol, side, amount, params={}):
        """🚀 ENHANCED: Market order now converts to aggressive intelligent limit order"""
        # 🔧 FIX 2: Validate minimum order size before placing order
        if not validate_minimum_order_size_global(symbol, amount):
            return None

        print(f"⚠️ Market order requested - converting to aggressive intelligent limit order for better execution")

        # Convert market order to aggressive intelligent limit order
        return place_intelligent_limit_order(symbol, side, amount, confidence=95.0, params=params)

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Fetch OHLCV data using ccxt"""
        if exchange:
            try:
                return exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            except Exception as e:
                print(f"Error fetching OHLCV: {e}")
                return []  # Return empty data instead of mock
        else:
            print("No exchange available for OHLCV data")
            return []

    def fetch_order_book(symbol):
        """Fetch order book using ccxt"""
        if exchange:
            try:
                return exchange.fetch_order_book(symbol)
            except Exception as e:
                print(f"Error fetching order book: {e}")
                return {'bids': [], 'asks': []}  # Return empty orderbook instead of mock
        else:
            print("No exchange available for orderbook data")
            return {'bids': [], 'asks': []}

    def fetch_best_bid(symbol):
        """Fetch best bid using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'bids' in ob and ob['bids']:
                return ob['bids'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best bid: {e}")
            return None

    def fetch_best_ask(symbol):
        """Fetch best ask using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'asks' in ob and ob['asks']:
                return ob['asks'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best ask: {e}")
            return None

    # Removed mock data generation - using real data from live data manager

    def close_all_positions():
        """Close all positions - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                # Get all open positions
                positions = exchange.fetch_positions()
                closed_count = 0
                for position in positions:
                    if position['contracts'] > 0:  # Has open position
                        symbol = position['symbol']
                        side = 'sell' if position['side'] == 'long' else 'buy'
                        amount = position['contracts']
                        exchange.create_market_order(symbol, side, amount)
                        closed_count += 1
                print(f"LIVE: Closed {closed_count} positions")
                return True
            except Exception as e:
                print(f"Error closing positions: {e}")
                return False
        else:
            print("DEMO: Closing all positions")
            return True

    def cancel_all_orders():
        """Cancel all orders - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                cancelled_orders = exchange.cancel_all_orders()
                print(f"LIVE: Cancelled {len(cancelled_orders)} orders")
                return True
            except Exception as e:
                print(f"Error cancelling orders: {e}")
                return False
        else:
            print("DEMO: Cancelling all orders")
            return True

    def set_leverage(symbol, leverage):
        """Set leverage - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                exchange.set_leverage(leverage, symbol)
                print(f"LIVE: Set leverage {leverage}x for {symbol}")
                return True
            except Exception as e:
                print(f"Error setting leverage: {e}")
                return False
        else:
            print(f"DEMO: Set leverage {leverage}x for {symbol}")
            return True

except ImportError as e:
    print(f"⚠ Could not import trading functions: {e}")
    print("⚠ Using mock implementations")

    # Fallback mock implementations
    def place_limit_order(symbol, side, amount, price, params=None):
        """Mock limit order placement"""
        if params is None:
            params = {}
        # 🔧 FIX 2: Mock validation for minimum order size
        if amount < 1.0:  # Simple mock minimum
            print(f"MOCK: Order amount {amount} below minimum 1.0 for {symbol}")
            return None
        print(f"MOCK: Placed {side} limit order for {amount} {symbol} at {price}")
        return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params=None):
        """Mock market order placement"""
        if params is None:
            params = {}
        # 🔧 FIX 2: Mock validation for minimum order size
        if amount < 1.0:  # Simple mock minimum
            print(f"MOCK: Order amount {amount} below minimum 1.0 for {symbol}")
            return None
        print(f"MOCK: Placed {side} market order for {amount} {symbol}")
        return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_best_bid(symbol):
        """Mock best bid"""
        ob = fetch_order_book(symbol)
        return ob['bids'][0][0] if ob['bids'] else None

    def fetch_best_ask(symbol):
        """Mock best ask"""
        ob = fetch_order_book(symbol)
        return ob['asks'][0][0] if ob['asks'] else None

    def close_all_positions():
        """Mock close all positions"""
        print("MOCK: Closing all positions")
        return True

    def cancel_all_orders():
        """Mock cancel all orders"""
        print("MOCK: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Mock set leverage"""
        print(f"MOCK: Set leverage {leverage}x for {symbol}")
        return True

    exchange = None
    demo_mode = True

# Import GUI components
try:
    from gui.model_selector_widget import ModelSelectorWidget
    print("✓ Model selector widget loaded")
except ImportError as e:
    print(f"⚠ Could not import model selector widget: {e}")
    ModelSelectorWidget = None

# Import symbol scanner
try:
    from symbol_scanner import SymbolScanner, SymbolScannerConfig
    print("✓ Symbol scanner loaded")
except ImportError as e:
    print(f"⚠ Could not import symbol scanner: {e}")
    SymbolScanner = None
    SymbolScannerConfig = None

# Direct CCXT integration for positions and orders - no more me2_stable.py dependency!
import ccxt
import yaml
import os

# Initialize CCXT exchange directly
exchange = None

def initialize_ccxt_exchange():
    """Initialize CCXT exchange with credentials"""
    global exchange
    try:
        # Load credentials - check multiple possible locations
        credentials_paths = [
            'credentials.yaml',
            'config/credentials.yaml',
            os.path.join('config', 'credentials.yaml')
        ]

        credentials_path = None
        for path in credentials_paths:
            if os.path.exists(path):
                credentials_path = path
                break

        if credentials_path:
            with open(credentials_path, 'r') as f:
                credentials = yaml.safe_load(f)

            # Get API credentials - try multiple possible key names
            api_key = credentials.get('apiKey', '') or credentials.get('api_key', '') or credentials.get('API_KEY', '')
            secret_key = credentials.get('secretKey', '') or credentials.get('secret_key', '') or credentials.get('SECRET_KEY', '') or credentials.get('secret', '')

            if api_key and secret_key:
                # Initialize HTX exchange
                exchange = ccxt.huobi({
                    'apiKey': api_key,
                    'secret': secret_key,
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'swap',  # Use swap for futures
                    },
                    'urls': {
                        'api': {
                            'public': 'https://api.huobi.pro',
                            'private': 'https://api.huobi.pro',
                            'swap': 'https://api.hbdm.com',
                        }
                    },
                    'sandbox': False,
                })

                # Load markets
                exchange.load_markets()
                return True
            else:
                print("❌ No API credentials found in credentials.yaml")
                return False
        else:
            print("❌ No credentials.yaml file found")
            return False
    except Exception as e:
        print(f"❌ Error initializing CCXT exchange: {e}")
        import traceback
        traceback.print_exc()
        return False

def fetch_open_positions(symbol=None):
    """Fetch open positions using CCXT directly"""
    try:
        if not exchange:
            return []

        # Fetch positions
        positions = exchange.fetch_positions()

        # Filter for open positions only
        open_positions = []
        for pos in positions:
            # Check different possible size fields
            size_fields = ['contracts', 'size', 'amount', 'notional']
            position_size = 0

            for field in size_fields:
                if field in pos and pos[field] is not None:
                    try:
                        position_size = float(pos[field])
                        if position_size > 0:
                            break
                    except (ValueError, TypeError):
                        continue

            if position_size > 0:  # Only positions with size > 0
                open_positions.append({
                    'symbol': pos.get('symbol', 'Unknown'),
                    'side': pos.get('side', 'Unknown'),
                    'size': position_size,
                    'contracts': position_size,
                    'entryPrice': pos.get('entryPrice', 0) or pos.get('markPrice', 0),
                    'unrealizedPnl': pos.get('unrealizedPnl', 0) or pos.get('pnl', 0),
                    'percentage': pos.get('percentage', 0)
                })

        # Filter by symbol if specified
        if symbol:
            open_positions = [p for p in open_positions if p['symbol'] == symbol]

        return open_positions

    except Exception as e:
        print(f"❌ Error fetching positions: {e}")
        return []

def fetch_open_orders(symbol=None):
    """Fetch open orders using CCXT directly"""
    try:
        if not exchange:
            return []

        # Fetch open orders
        if symbol:
            orders = exchange.fetch_open_orders(symbol)
        else:
            orders = exchange.fetch_open_orders()

        # Format orders
        formatted_orders = []
        for order in orders:
            formatted_orders.append({
                'id': order['id'],
                'symbol': order['symbol'],
                'side': order['side'],
                'amount': order['amount'],
                'price': order['price'],
                'type': order['type'],
                'status': order['status'],
                'timestamp': order['timestamp']
            })

        return formatted_orders

    except Exception as e:
        print(f"❌ Error fetching orders: {e}")
        return []

# Initialize the exchange (reduced logging)
initialize_ccxt_exchange()

# Matrix Theme
class MatrixTheme:
    """Matrix-inspired theme colors and styling"""
    
    # Colors
    BLACK = "#000000"
    BACKGROUND = "#000000"
    GREEN = "#00FF00"
    TEXT = "#00FF00"
    DARK_GREEN = "#003300"
    MID_GREEN = "#006600"
    LIGHT_GREEN = "#00CC00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    WHITE = "#FFFFFF"
    GRAY = "#666666"
    BRIGHT_GREEN = "#00FF88"
    CYAN = "#00FFFF"  # Add missing CYAN color
    BLUE = "#0088FF"  # Add missing BLUE color

    # Font settings
    FONT_FAMILY = "Courier New"
    FONT_SIZE = 14
    FONT_SIZE_SMALL = 14
    FONT_SIZE_MEDIUM = 14
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: 'Courier New', monospace;
            font-size: {cls.FONT_SIZE_MEDIUM}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.DARK_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.GREEN};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
            font-weight: bold;
        }}
        
        QGroupBox {{
            border: 2px solid {cls.DARK_GREEN};
            border-radius: 5px;
            margin: 5px;
            padding-top: 10px;
            color: {cls.GREEN};
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.LIGHT_GREEN};
        }}
        
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
            padding: 8px 16px;
            font-weight: bold;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.LIGHT_GREEN};
            color: {cls.BLACK};
        }}
        
        QLabel {{
            color: {cls.GREEN};
            background-color: transparent;
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            font-family: 'Courier New', monospace;
        }}
        
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.DARK_GREEN};
            color: {cls.LIGHT_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 5px;
            border: 1px solid {cls.GREEN};
            font-weight: bold;
        }}
        
        QStatusBar {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-top: 1px solid {cls.DARK_GREEN};
        }}
        
        QCheckBox {{
            color: {cls.GREEN};
            spacing: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.GREEN};
        }}
        """

# Simple Trading Interface
class EpinnoxTradingInterface(QMainWindow):
    """Simplified Epinnox Trading Interface"""

    # Class variable to store the instance for fallback functions
    _instance = None

    def __init__(self):
        super().__init__()
        # Store the instance for fallback functions
        EpinnoxTradingInterface._instance = self
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(50, 50, 1400, 900)  # Larger window for better layout
        self.setMinimumSize(1200, 800)  # Minimum size to prevent cramping

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Analysis control
        self.analysis_timer = None
        self.is_analyzing = False

        # Performance optimization: GUI update batching
        self.pending_gui_updates = {}
        self.gui_update_timer = QTimer()
        self.gui_update_timer.timeout.connect(self.apply_batched_gui_updates)
        self.gui_update_timer.setSingleShot(True)

        # Track last position count for automatic updates
        self.last_position_count = 0

        try:
            print("Setting up UI...")
            self.setup_ui()
            print("✓ UI setup complete")

            print("Setting up menu bar...")
            self.setup_menu_bar()
            print("✓ Menu bar setup complete")

            print("Setting up timers...")
            self.setup_timers()
            print("✓ Timers setup complete")

            print("Setting up symbol scanner...")
            self.setup_symbol_scanner()
            print("✓ Symbol scanner setup complete")

            # 🚀 ENHANCED: Initialize LLM Action Executors with intelligent limit order system
            print("Setting up intelligent limit order system...")
            self.setup_intelligent_limit_order_system()
            print("✓ Intelligent limit order system setup complete")

        except Exception as e:
            print(f"❌ Error during initialization: {e}")
            import traceback
            traceback.print_exc()
            raise

        # Verify menu bar exists
        menubar = self.menuBar()
        if menubar and menubar.actions():
            print(f"✓ Menu bar verified: {len(menubar.actions())} menu items")
            for action in menubar.actions():
                print(f"  - {action.text()}")
        else:
            print("❌ Menu bar missing or empty!")

        print("✓ Epinnox Trading Interface initialized")

    def setup_intelligent_limit_order_system(self):
        """🚀 NEW: Setup intelligent limit order system with LLM action executors"""
        try:
            # Initialize LLM Action Executors with intelligent limit order system
            from core.llm_action_executors import LLMActionExecutors

            # Check if real trading interface is available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.llm_action_executors = LLMActionExecutors(self.real_trading, self)
                print("🚀 LLM Action Executors initialized with real trading interface")
            else:
                # Initialize without real trading for now
                self.llm_action_executors = LLMActionExecutors(None, self)
                print("⚠️ LLM Action Executors initialized without real trading interface")

            # Log intelligent limit order system status
            if (self.llm_action_executors and
                self.llm_action_executors.limit_order_manager):
                print("✅ Intelligent Limit Order Manager active")
                print(f"   📊 Scalping config: {self.llm_action_executors.scalping_config}")
            else:
                print("⚠️ Intelligent Limit Order Manager not available - using fallback")

        except Exception as e:
            print(f"❌ Failed to initialize intelligent limit order system: {e}")
            self.llm_action_executors = None

    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Main content (header is now in menu bar)
        content_widget = self.create_content()
        layout.addWidget(content_widget)

        # Status bar
        self.statusBar().showMessage("Epinnox v6 System Ready")

    def setup_menu_bar(self):
        """Setup menu bar with layout and settings options"""
        menubar = self.menuBar()

        # Ensure menu bar is visible
        menubar.setVisible(True)
        menubar.setNativeMenuBar(False)  # Force Qt menu bar instead of native

        # print("Creating menu bar...")

        # Layout menu
        layout_menu = menubar.addMenu('Layout')
        # print("✓ Layout menu created")

        # Save layout action
        save_layout_action = QAction('Save Layout', self)
        save_layout_action.setShortcut('Ctrl+S')
        save_layout_action.triggered.connect(self.save_layout)
        layout_menu.addAction(save_layout_action)

        # Load layout action
        load_layout_action = QAction('Load Layout', self)
        load_layout_action.setShortcut('Ctrl+L')
        load_layout_action.triggered.connect(self.load_layout)
        layout_menu.addAction(load_layout_action)

        layout_menu.addSeparator()

        # Reset to default layout
        reset_layout_action = QAction('Reset to Default', self)
        reset_layout_action.setShortcut('Ctrl+R')
        reset_layout_action.triggered.connect(self.reset_layout)
        layout_menu.addAction(reset_layout_action)

        # Settings menu
        settings_menu = menubar.addMenu('Settings')
        # print("✓ Settings menu created")

        # Model selection action
        model_settings_action = QAction('Model Selection', self)
        model_settings_action.setShortcut('Ctrl+M')
        model_settings_action.triggered.connect(self.show_model_settings)
        settings_menu.addAction(model_settings_action)

        settings_menu.addSeparator()

        # Preferences action
        preferences_action = QAction('Preferences', self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)

        # About menu
        about_menu = menubar.addMenu('About')
        print("✓ About menu created")

        # About Epinnox action
        about_action = QAction('About Epinnox', self)
        about_action.triggered.connect(self.show_about)
        about_menu.addAction(about_action)

        # Add status widgets to the right side of menu bar
        from PyQt5.QtCore import QTimer


        # Create compact status widget container
        status_widget = QWidget()
        status_widget.setMaximumWidth(650)  # Increased width to fit all status info
        status_widget.setMinimumWidth(500)  # Ensure minimum width
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 2, 5, 5)
        status_layout.setSpacing(8)  # Slightly more spacing for readability

        # System title (shorter)
        title_label = QLabel("EPINNOX v6")
        title_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(title_label)

        # System status
        self.system_status_label = QLabel("READY")
        self.system_status_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(self.system_status_label)

        # Production mode indicator
        mode_text = "🔴 LIVE" if not demo_mode else "🟡 DEMO"
        mode_color = MatrixTheme.RED if not demo_mode else MatrixTheme.YELLOW
        self.mode_label = QLabel(mode_text)
        self.mode_label.setStyleSheet(f"""
            color: {mode_color};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            padding: 2px 6px;
            border: 1px solid {mode_color};
            border-radius: 3px;
        """)
        status_layout.addWidget(self.mode_label)

        # Balance display (like in your image: Equity: $209.34 Free: $209.34)
        self.balance_label = QLabel("Equity: $-- Free: $--")
        self.balance_label.setMinimumWidth(200)  # Ensure enough space for balance text
        self.balance_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            padding: 2px 6px;
            border: 1px solid {MatrixTheme.YELLOW};
            border-radius: 3px;
            background-color: rgba(255, 255, 0, 0.1);
        """)
        status_layout.addWidget(self.balance_label)

        # Current time
        self.time_label = QLabel()
        self.time_label.setMinimumWidth(150)  # Ensure enough space for timestamp
        self.time_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(self.time_label)

        # Add status widget to menu bar (right side)
        menubar.setCornerWidget(status_widget, Qt.TopRightCorner)

        # Setup timer to update time (reduced frequency to prevent lag)
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(5000)  # Update every 5 seconds (reduced from 1 second)
        self.update_time_display()  # Initial update

        # Style menu bar - ensure menu items are visible with high contrast
        print("Applying menu bar styling...")
        menubar.setStyleSheet(f"""
            QMenuBar {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                border-bottom: 2px solid {MatrixTheme.GREEN};
                padding: 4px;
                min-height: 25px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                font-weight: bold;
            }}
            QMenuBar::item {{
                background-color: transparent;
                color: {MatrixTheme.GREEN};
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
                border: 1px solid transparent;
            }}
            QMenuBar::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.GREEN};
            }}
            QMenuBar::item:pressed {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
            }}
            QMenu {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                border: 2px solid {MatrixTheme.GREEN};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 4px;
            }}
            QMenu::item {{
                padding: 8px 24px;
                background-color: transparent;
                color: {MatrixTheme.GREEN};
                border: 1px solid transparent;
            }}
            QMenu::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.GREEN};
            }}
        """)

        print("Menu bar setup complete")  # Debug

    def save_layout(self):
        """Save current layout to settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Save main splitter state
            if hasattr(self, 'main_splitter'):
                settings.setValue('main_splitter', self.main_splitter.saveState())

            # Save individual column splitter states
            if hasattr(self, 'left_splitter'):
                settings.setValue('left_splitter', self.left_splitter.saveState())
            if hasattr(self, 'middle_splitter'):
                settings.setValue('middle_splitter', self.middle_splitter.saveState())
            if hasattr(self, 'right_splitter'):
                settings.setValue('right_splitter', self.right_splitter.saveState())

            # Save window geometry
            settings.setValue('geometry', self.saveGeometry())
            settings.setValue('windowState', self.saveState())

            self.statusBar().showMessage("Layout saved successfully", 2000)
            print("✓ Layout saved to settings")

        except Exception as e:
            print(f"Error saving layout: {e}")
            self.statusBar().showMessage("Error saving layout", 2000)

    def load_layout(self):
        """Load layout from settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Restore main splitter state
            if hasattr(self, 'main_splitter'):
                state = settings.value('main_splitter')
                if state:
                    self.main_splitter.restoreState(state)

            # Restore individual column splitter states
            if hasattr(self, 'left_splitter'):
                state = settings.value('left_splitter')
                if state:
                    self.left_splitter.restoreState(state)
            if hasattr(self, 'middle_splitter'):
                state = settings.value('middle_splitter')
                if state:
                    self.middle_splitter.restoreState(state)
            if hasattr(self, 'right_splitter'):
                state = settings.value('right_splitter')
                if state:
                    self.right_splitter.restoreState(state)

            # Restore window geometry
            geometry = settings.value('geometry')
            if geometry:
                self.restoreGeometry(geometry)
            window_state = settings.value('windowState')
            if window_state:
                self.restoreState(window_state)

            self.statusBar().showMessage("Layout loaded successfully", 2000)
            print("✓ Layout loaded from settings")

        except Exception as e:
            print(f"Error loading layout: {e}")
            self.statusBar().showMessage("Error loading layout", 2000)

    def reset_layout(self):
        """Reset to default layout"""
        try:
            # Reset main splitter to default sizes
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([400, 450, 600])

            # Reset column splitters to default sizes
            if hasattr(self, 'left_splitter'):
                self.left_splitter.setSizes([120, 100, 300, 150])
            if hasattr(self, 'middle_splitter'):
                self.middle_splitter.setSizes([150, 200, 150, 200])
            if hasattr(self, 'right_splitter'):
                self.right_splitter.setSizes([250, 400])

            # Reset window size
            self.setGeometry(50, 50, 1400, 900)

            self.statusBar().showMessage("Layout reset to default", 2000)
            print("✓ Layout reset to default")

        except Exception as e:
            print(f"Error resetting layout: {e}")
            self.statusBar().showMessage("Error resetting layout", 2000)

    def show_model_settings(self):
        """Show model selection settings dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle("Model Selection Settings")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # Add model selector widget to dialog
            if hasattr(self, 'model_selector') and self.model_selector:
                # Move existing model selector to dialog temporarily
                model_widget = self.model_selector
            elif ModelSelectorWidget:
                # Create new model selector for dialog
                model_widget = ModelSelectorWidget()
                model_widget.model_switch_requested.connect(self.on_model_switch_requested)
                model_widget.refresh_requested.connect(self.on_model_refresh_requested)
            else:
                # Fallback label
                model_widget = QLabel("Model selector not available")

            layout.addWidget(model_widget)

            # Buttons
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing model settings: {e}")

    def show_preferences(self):
        """Show preferences dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QCheckBox

            dialog = QDialog(self)
            dialog.setWindowTitle("Preferences")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # Add some preference options
            layout.addWidget(QLabel("Trading Preferences:"))

            auto_trade_cb = QCheckBox("Enable Auto Trading")
            layout.addWidget(auto_trade_cb)

            debug_mode_cb = QCheckBox("Debug Mode")
            layout.addWidget(debug_mode_cb)

            layout.addStretch()

            # Buttons
            button_layout = QHBoxLayout()
            save_btn = QPushButton("Save")
            cancel_btn = QPushButton("Cancel")
            save_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addStretch()
            button_layout.addWidget(save_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QCheckBox {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing preferences: {e}")

    def show_about(self):
        """Show about dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
            from PyQt5.QtCore import Qt

            dialog = QDialog(self)
            dialog.setWindowTitle("About Epinnox")
            dialog.setModal(True)
            dialog.resize(500, 350)

            layout = QVBoxLayout(dialog)

            # Title
            title = QLabel("Epinnox v6 Trading System")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet(f"""
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                font-weight: bold;
                color: {MatrixTheme.GREEN};
                margin: 10px;
            """)
            layout.addWidget(title)

            # Description
            description = QLabel("""
            Advanced AI-Powered Trading System

            Features:
            • Real-time market data analysis
            • ML/LLM signal integration
            • Dynamic leverage management
            • Professional trading interface
            • Comprehensive risk management

            Combining numerical analysis with language models
            for intelligent trading decisions.
            """)
            description.setAlignment(Qt.AlignCenter)
            description.setWordWrap(True)
            layout.addWidget(description)

            layout.addStretch()

            # Close button
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QLabel {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing about dialog: {e}")

    def update_time_display(self):
        """Update the time display in the menu bar"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)
        except Exception as e:
            print(f"Error updating time display: {e}")

    def update_balance_display(self):
        """Update the balance display in the menu bar"""
        try:
            # Fetch balance from real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                balance = self.real_trading.get_balance_info()

                if balance:
                    usdt_info = balance.get('USDT', {})
                    free_balance = usdt_info.get('free', 0)
                    total_balance = usdt_info.get('total', 0)

                    # Update menu bar balance display
                    if hasattr(self, 'balance_label'):
                        balance_text = f"Equity: ${total_balance:.2f} Free: ${free_balance:.2f}"
                        self.balance_label.setText(balance_text)

                        # Color coding based on balance
                        if free_balance > 0:
                            color = MatrixTheme.YELLOW  # Yellow for positive balance
                        else:
                            color = MatrixTheme.RED  # Red for zero/negative balance

                        self.balance_label.setStyleSheet(f"""
                            color: {color};
                            font-weight: bold;
                            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                            padding: 2px 6px;
                            border: 1px solid {color};
                            border-radius: 3px;
                            background-color: rgba(255, 255, 0, 0.1);
                        """)
        except Exception as e:
            print(f"❌ Error updating balance display: {e}")
    
    def create_content(self):
        """Create main content area with resizable splitters"""
        # Main horizontal splitter
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing completely

        # Left column - Controls and Analysis
        self.left_splitter = self.create_left_column()
        self.main_splitter.addWidget(self.left_splitter)

        # Middle column - Analysis Results
        self.middle_splitter = self.create_middle_column()
        self.main_splitter.addWidget(self.middle_splitter)

        # Right column - Chart and Trading
        self.right_splitter = self.create_right_column()
        self.main_splitter.addWidget(self.right_splitter)

        # Set initial sizes (proportional)
        self.main_splitter.setSizes([400, 450, 600])  # Left, Middle, Right widths

        # Style the splitter
        self.main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.MID_GREEN};
                width: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.GREEN};
            }}
        """)

        # Create container widget
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.main_splitter)

        return container

    def create_left_column(self):
        """Create left column with resizable panels"""
        # Create vertical splitter for left column panels
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.setChildrenCollapsible(False)

        # Symbol selection and controls
        symbol_panel = self.create_symbol_panel()
        left_splitter.addWidget(symbol_panel)

        # Current analysis
        current_analysis_panel = self.create_current_analysis_panel()
        left_splitter.addWidget(current_analysis_panel)

        # LLM Analysis (expanded)
        llm_panel = self.create_llm_analysis_panel()
        left_splitter.addWidget(llm_panel)

        # Risk Warnings (compact)
        risk_warnings_panel = self.create_risk_warnings_panel()
        left_splitter.addWidget(risk_warnings_panel)

        # Set initial sizes for left column panels (more space for LLM analysis)
        left_splitter.setSizes([100, 80, 450, 120])  # Symbol, Current, LLM (expanded), Risk

        # Style the vertical splitter
        left_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return left_splitter

    def create_middle_column(self):
        """Create middle column with resizable panels"""
        # Create vertical splitter for middle column panels
        middle_splitter = QSplitter(Qt.Vertical)
        middle_splitter.setChildrenCollapsible(False)

        # ML Models Status (compact table)
        ml_models_panel = self.create_ml_models_panel()
        middle_splitter.addWidget(ml_models_panel)

        # Final Trading Verdict (moved to middle column)
        final_verdict_panel = self.create_final_verdict_panel()
        middle_splitter.addWidget(final_verdict_panel)

        # Market Analysis (compact)
        market_analysis_panel = self.create_market_analysis_panel()
        middle_splitter.addWidget(market_analysis_panel)

        # Set initial sizes for middle column panels (ML models, Final Verdict, Market)
        middle_splitter.setSizes([300, 250, 200])  # ML (300px), Final Verdict (250px), Market (200px)

        # Style the vertical splitter
        middle_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return middle_splitter

    def create_right_column(self):
        """Create right column with resizable panels"""
        # Create vertical splitter for right column panels
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setChildrenCollapsible(False)

        # Manual Trading Controls (compact)
        trading_panel = self.create_manual_trading_panel()
        right_splitter.addWidget(trading_panel)

        # Leverage Analysis Panel (compact)
        leverage_panel = self.create_leverage_panel()
        right_splitter.addWidget(leverage_panel)

        # Historical Final Verdicts Panel
        historical_verdicts_panel = self.create_historical_verdicts_panel()
        right_splitter.addWidget(historical_verdicts_panel)

        # LLM Orchestrator Panel
        orchestrator_panel = self.create_llm_orchestrator_panel()
        right_splitter.addWidget(orchestrator_panel)

        # Open Orders Panel (expandable)
        orders_panel = self.create_open_orders_panel()
        right_splitter.addWidget(orders_panel)

        # Set initial sizes for right column panels
        right_splitter.setSizes([200, 150, 200, 250, 300])  # Trading: 200px, Leverage: 150px, History: 200px, Orchestrator: 250px, Orders: 300px

        # Style the vertical splitter
        right_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return right_splitter

    def create_symbol_panel(self):
        """Create symbol selection panel"""
        group = QGroupBox("Symbol Selection")
        layout = QVBoxLayout(group)

        # Trading Symbol
        symbol_layout = QHBoxLayout()
        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"])

        # CRITICAL: Connect symbol change handler for manual symbol changes
        self.symbol_combo.currentTextChanged.connect(self.on_manual_symbol_changed)

        symbol_layout.addWidget(self.symbol_combo)
        layout.addLayout(symbol_layout)

        # Checkboxes
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.live_data_checkbox.setChecked(True)
        layout.addWidget(self.live_data_checkbox)

        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (60s)")
        self.auto_refresh_checkbox.setChecked(True)
        layout.addWidget(self.auto_refresh_checkbox)

        # Dynamic Symbol Scanner
        self.dynamic_scan_cb = QCheckBox("🤖 Auto-Select Best Symbol")
        self.dynamic_scan_cb.setToolTip("Automatically scan and select the best trading symbol based on real-time metrics")
        self.dynamic_scan_cb.stateChanged.connect(self.on_dynamic_scan_toggled)
        layout.addWidget(self.dynamic_scan_cb)

        # Scanner status label
        self.scanner_status_label = QLabel("Scanner: Disabled")
        self.scanner_status_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 2px;
        """)
        layout.addWidget(self.scanner_status_label)

        # ScalperGPT Autonomous Trading section
        scalper_group = QGroupBox("🤖 ScalperGPT Controls")
        scalper_layout = QVBoxLayout(scalper_group)
        scalper_layout.setSpacing(4)
        scalper_layout.setContentsMargins(5, 2, 5, 5)

        # Auto trader checkbox
        self.auto_trader_checkbox = QCheckBox("🤖 ScalperGPT Auto Trader")
        self.auto_trader_checkbox.setChecked(False)
        self.auto_trader_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {MatrixTheme.GREEN};
                border: 2px solid {MatrixTheme.GREEN};
            }}
            QCheckBox::indicator:unchecked {{
                background-color: transparent;
                border: 2px solid {MatrixTheme.GRAY};
            }}
        """)
        self.auto_trader_checkbox.stateChanged.connect(self.on_auto_trader_toggled)
        scalper_layout.addWidget(self.auto_trader_checkbox)

        # Risk percentage control
        risk_layout = QHBoxLayout()
        risk_layout.addWidget(QLabel("Risk %:"))
        self.risk_pct_spinbox = QDoubleSpinBox()
        self.risk_pct_spinbox.setRange(0.5, 5.0)
        self.risk_pct_spinbox.setDecimals(1)
        self.risk_pct_spinbox.setValue(2.0)
        self.risk_pct_spinbox.setSuffix("%")
        self.risk_pct_spinbox.setToolTip("Risk percentage per trade (0.5-5.0%)")
        self.risk_pct_spinbox.setStyleSheet(f"""
            QDoubleSpinBox {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                background-color: {MatrixTheme.BLACK};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
        """)
        risk_layout.addWidget(self.risk_pct_spinbox)
        scalper_layout.addLayout(risk_layout)

        # Emergency stop button
        self.emergency_stop_btn = QPushButton("🚨 EMERGENCY STOP")
        self.emergency_stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 6px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #ff6666;
            }}
        """)
        self.emergency_stop_btn.clicked.connect(self.emergency_stop_trading)
        scalper_layout.addWidget(self.emergency_stop_btn)

        # Daily trade limit
        limit_layout = QHBoxLayout()
        limit_layout.addWidget(QLabel("Daily Limit:"))
        self.daily_limit_spinbox = QSpinBox()
        self.daily_limit_spinbox.setRange(1, 100)
        self.daily_limit_spinbox.setValue(10)
        self.daily_limit_spinbox.setToolTip("Maximum trades per day")
        self.daily_limit_spinbox.setStyleSheet(f"""
            QSpinBox {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                background-color: {MatrixTheme.BLACK};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
        """)
        limit_layout.addWidget(self.daily_limit_spinbox)
        scalper_layout.addLayout(limit_layout)

        layout.addWidget(scalper_group)

        # Buttons
        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_button)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_analysis)
        layout.addWidget(self.stop_button)

        return group

    def create_current_analysis_panel(self):
        """Create current analysis panel"""
        group = QGroupBox("Current Analysis")
        layout = QVBoxLayout(group)

        self.decision_label = QLabel("Decision: WAIT")
        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.decision_label)

        self.confidence_label = QLabel("Confidence: 86.0%")
        layout.addWidget(self.confidence_label)

        self.last_update_label = QLabel("Last Update: 19:32:58")
        layout.addWidget(self.last_update_label)

        return group

    def create_ml_models_panel(self):
        """Create enhanced ML models status panel with ScalperGPT features"""
        group = QGroupBox("🤖 ScalperGPT Adaptive Ensemble")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Ensemble summary header
        ensemble_header = QLabel("Adaptive Ensemble Analysis")
        ensemble_header.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 5px;
            border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
        """)
        layout.addWidget(ensemble_header)

        # Ensemble metrics display
        metrics_layout = QHBoxLayout()

        self.ensemble_vote_label = QLabel("Vote: WAIT")
        self.ensemble_vote_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.ensemble_vote_label)

        self.ensemble_confidence_label = QLabel("Avg: 50.0%")
        self.ensemble_confidence_label.setStyleSheet(f"color: {MatrixTheme.TEXT}; font-weight: bold;")
        metrics_layout.addWidget(self.ensemble_confidence_label)

        self.ensemble_score_label = QLabel("Score: 0.00")
        self.ensemble_score_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.ensemble_score_label)

        self.ensemble_consensus_label = QLabel("Consensus: 0%")
        self.ensemble_consensus_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.ensemble_consensus_label)

        layout.addLayout(metrics_layout)

        # Create enhanced table with 6 columns for ScalperGPT features
        self.ml_models_table = QTableWidget(8, 6)
        header = self.ml_models_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        self.ml_models_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
                padding: 2px;
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 3px;
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        # Enhanced headers for ScalperGPT
        self.ml_models_table.setHorizontalHeaderLabels([
            "Model", "Decision", "Confidence", "Weight", "Accuracy", "Status"
        ])
        self.ml_models_table.verticalHeader().setVisible(False)

        # Make table compact but readable
        self.ml_models_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.ml_models_table.setMinimumHeight(220)
        self.ml_models_table.setMaximumHeight(280)
        self.ml_models_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.ml_models_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Initialize ScalperGPT model data
        models_data = [
            ["SVM", "WAIT", "00.0%", "1.20", "52.0%", "ACTIVE"],
            ["Random Forest", "WAIT", "00.0%", "1.10", "54.5%", "ACTIVE"],
            ["LSTM", "WAIT", "00.0%", "0.90", "48.2%", "PRUNED"],
            ["RSI Model", "WAIT", "00.0%", "1.00", "51.8%", "ACTIVE"],
            ["VWAP Model", "WAIT", "00.0%", "1.10", "55.1%", "ACTIVE"],
            ["Orderflow Model", "WAIT", "00.0%", "0.80", "49.3%", "PRUNED"],
            ["Volatility Model", "WAIT", "00.0%", "1.00", "53.2%", "ACTIVE"],
            ["Sentiment Model", "WAIT", "00.0%", "0.70", "47.8%", "PRUNED"]
        ]

        for row, (model, decision, confidence, weight, accuracy, status) in enumerate(models_data):
            # Model name
            model_item = QTableWidgetItem(model)
            model_item.setForeground(QColor(MatrixTheme.TEXT))
            self.ml_models_table.setItem(row, 0, model_item)

            # Decision with color coding
            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)

            # Confidence percentage
            confidence_item = QTableWidgetItem(confidence)
            confidence_item.setForeground(QColor(MatrixTheme.TEXT))
            self.ml_models_table.setItem(row, 2, confidence_item)

            # Dynamic weight
            weight_item = QTableWidgetItem(weight)
            weight_val = float(weight)
            if weight_val > 1.0:
                weight_item.setForeground(QColor(MatrixTheme.GREEN))
            elif weight_val < 0.9:
                weight_item.setForeground(QColor(MatrixTheme.RED))
            else:
                weight_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 3, weight_item)

            # Accuracy tracking
            accuracy_item = QTableWidgetItem(accuracy)
            acc_val = float(accuracy.replace('%', ''))
            if acc_val >= 52.0:
                accuracy_item.setForeground(QColor(MatrixTheme.GREEN))
            else:
                accuracy_item.setForeground(QColor(MatrixTheme.RED))
            self.ml_models_table.setItem(row, 4, accuracy_item)

            # Status indicator
            status_item = QTableWidgetItem(status)
            if status == "ACTIVE":
                status_item.setForeground(QColor(MatrixTheme.GREEN))
            else:  # PRUNED
                status_item.setForeground(QColor(MatrixTheme.RED))
            self.ml_models_table.setItem(row, 5, status_item)

        layout.addWidget(self.ml_models_table)

        return group

    def create_llm_analysis_panel(self):
        """Create LLM analysis panel with expanded text area"""
        group = QGroupBox("LLM Analysis")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)

        # LLM Decision
        self.llm_decision_label = QLabel("LLM Decision: WAIT")
        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.llm_decision_label)

        # LLM Confidence
        self.llm_confidence_label = QLabel("LLM Confidence: 75.0%")
        layout.addWidget(self.llm_confidence_label)

        # LLM Reasoning (scrollable)
        reasoning_label = QLabel("LLM Reasoning:")
        layout.addWidget(reasoning_label)

        self.llm_reasoning_text = QTextEdit()
        self.llm_reasoning_text.setReadOnly(True)
        self.llm_reasoning_text.setMaximumHeight(200)  # Increased from 100
        self.llm_reasoning_text.setMinimumHeight(120)  # Increased from 60
        self.llm_reasoning_text.setWordWrapMode(QTextOption.WordWrap)

        # Ensure it expands to fill available space
        self.llm_reasoning_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Apply Matrix theme scrollbar styling
        self.llm_reasoning_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        self.llm_reasoning_text.setText("Market shows mixed signals. Technical indicators suggest consolidation while volume patterns indicate potential breakout. Recommend WAIT for clearer direction.")
        layout.addWidget(self.llm_reasoning_text)

        # Comprehensive Analysis Panel
        comprehensive_group = QGroupBox("🎭 Comprehensive Creative Analysis")
        comprehensive_layout = QVBoxLayout(comprehensive_group)

        # Comprehensive decision display
        self.comprehensive_decision_label = QLabel("Comprehensive Decision: Analyzing...")
        self.comprehensive_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 5px;
            border: 1px solid {MatrixTheme.GREEN};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
        """)
        comprehensive_layout.addWidget(self.comprehensive_decision_label)

        # Creative insight display
        self.creative_insight_text = QTextEdit()
        self.creative_insight_text.setMaximumHeight(120)
        self.creative_insight_text.setMinimumHeight(80)
        self.creative_insight_text.setReadOnly(True)
        self.creative_insight_text.setWordWrapMode(QTextOption.WordWrap)
        self.creative_insight_text.setPlaceholderText("Creative market insights will appear here...")
        self.creative_insight_text.setStyleSheet(f"""
            background-color: {MatrixTheme.BLACK};
            color: {MatrixTheme.GREEN};
            border: 1px solid {MatrixTheme.DARK_GREEN};
            font-family: {MatrixTheme.FONT_FAMILY};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 5px;
        """)
        comprehensive_layout.addWidget(self.creative_insight_text)

        layout.addWidget(comprehensive_group)

        return group

    def create_final_verdict_panel(self):
        """Create Enhanced ScalperGPT Trading Verdict panel"""
        group = QGroupBox("🤖 ScalperGPT Trading Decision")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # ScalperGPT status header
        scalper_header = QLabel("ScalperGPT Autonomous Decision Engine")
        scalper_header.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 3px;
            border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
        """)
        layout.addWidget(scalper_header)

        # Action and confidence display
        action_layout = QHBoxLayout()

        self.final_verdict_label = QLabel("ACTION: WAIT")
        self.final_verdict_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 6px;
            border: 2px solid {MatrixTheme.YELLOW};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
        """)
        action_layout.addWidget(self.final_verdict_label)

        self.scalper_confidence_label = QLabel("85%")
        self.scalper_confidence_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 6px;
            border: 1px solid {MatrixTheme.GREEN};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
            min-width: 60px;
        """)
        action_layout.addWidget(self.scalper_confidence_label)

        layout.addLayout(action_layout)

        # Enhanced trading parameters for ScalperGPT
        params_grid = QGridLayout()
        params_grid.setSpacing(3)

        # Quantity (ScalperGPT decides)
        params_grid.addWidget(QLabel("Quantity:"), 0, 0)
        self.final_position_size_label = QLabel("0.0000")
        self.final_position_size_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.final_position_size_label, 0, 1)

        # Leverage (ScalperGPT decides)
        params_grid.addWidget(QLabel("Leverage:"), 0, 2)
        self.final_leverage_label = QLabel("1x")
        self.final_leverage_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.final_leverage_label, 0, 3)

        # Risk percentage (ScalperGPT decides)
        params_grid.addWidget(QLabel("Risk %:"), 1, 0)
        self.final_risk_level_label = QLabel("2.0%")
        self.final_risk_level_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.final_risk_level_label, 1, 1)

        # Order type (ScalperGPT decides)
        params_grid.addWidget(QLabel("Order:"), 1, 2)
        self.scalper_order_type_label = QLabel("MARKET")
        self.scalper_order_type_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.scalper_order_type_label, 1, 3)

        # Stop loss
        params_grid.addWidget(QLabel("Stop Loss:"), 2, 0)
        self.final_stop_loss_label = QLabel("$0.000000")
        self.final_stop_loss_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.final_stop_loss_label, 2, 1)

        # Take profit
        params_grid.addWidget(QLabel("Take Profit:"), 2, 2)
        self.final_take_profit_label = QLabel("$0.000000")
        self.final_take_profit_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        params_grid.addWidget(self.final_take_profit_label, 2, 3)

        layout.addLayout(params_grid)

        # ScalperGPT decision context
        context_label = QLabel("Decision Context:")
        context_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        layout.addWidget(context_label)

        self.final_reasoning_text = QTextEdit()
        self.final_reasoning_text.setReadOnly(True)
        self.final_reasoning_text.setMaximumHeight(70)
        self.final_reasoning_text.setMinimumHeight(50)
        self.final_reasoning_text.setWordWrapMode(QTextOption.WordWrap)
        self.final_reasoning_text.setPlaceholderText("ScalperGPT decision context will appear here...")
        self.final_reasoning_text.setStyleSheet(f"""
            background-color: {MatrixTheme.BLACK};
            color: {MatrixTheme.GREEN};
            border: 1px solid {MatrixTheme.DARK_GREEN};
            font-family: {MatrixTheme.FONT_FAMILY};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 4px;
        """)
        layout.addWidget(self.final_reasoning_text)

        return group

    def create_historical_verdicts_panel(self):
        """Create enhanced historical ScalperGPT verdicts panel"""
        group = QGroupBox("📊 ScalperGPT Trading History")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Create enhanced table for ScalperGPT verdicts
        self.historical_verdicts_table = QTableWidget(0, 9)  # 9 columns for ScalperGPT data

        # Enhanced headers for ScalperGPT
        self.historical_verdicts_table.setHorizontalHeaderLabels([
            "Time", "Action", "Qty", "Lev", "Risk%", "Entry", "Exit", "PnL%", "Status"
        ])

        # Configure table appearance
        header = self.historical_verdicts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        self.historical_verdicts_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
                padding: 3px;
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.historical_verdicts_table.verticalHeader().setVisible(False)
        self.historical_verdicts_table.setAlternatingRowColors(True)
        self.historical_verdicts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.historical_verdicts_table.setMinimumHeight(150)
        self.historical_verdicts_table.setMaximumHeight(200)

        layout.addWidget(self.historical_verdicts_table)

        # Summary stats
        stats_layout = QHBoxLayout()

        self.total_verdicts_label = QLabel("Total: 0")
        self.total_verdicts_label.setStyleSheet(f"color: {MatrixTheme.TEXT}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.total_verdicts_label)

        self.success_rate_label = QLabel("Success: 0%")
        self.success_rate_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.success_rate_label)

        self.avg_confidence_label = QLabel("Avg Conf: 0%")
        self.avg_confidence_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.avg_confidence_label)

        layout.addLayout(stats_layout)

        # Initialize historical verdicts storage
        self.historical_verdicts = []

        # Initialize verdict tracking system
        self.active_verdicts = {}  # Track verdicts that are still being monitored
        self.verdict_tracking_timer = QTimer()
        self.verdict_tracking_timer.timeout.connect(self.update_verdict_tracking)
        self.verdict_tracking_timer.start(5000)  # Check every 5 seconds

        # Initialize autonomous trading system
        self.autonomous_trading_enabled = False
        self.autonomous_trading_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'daily_trades': 0,
            'last_trade_time': None,
            'max_daily_trades': 10,  # Safety limit
            'max_drawdown_limit': 15.0,  # 15% max drawdown
            'emergency_stop_triggered': False
        }

        return group

    def create_llm_orchestrator_panel(self):
        """Create LLM Orchestrator status and control panel"""
        group = QGroupBox("🧠 LLM Orchestrator Control Center")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Orchestrator status
        status_layout = QHBoxLayout()

        self.orchestrator_status_label = QLabel("Status: INITIALIZING")
        self.orchestrator_status_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            padding: 3px;
        """)
        status_layout.addWidget(self.orchestrator_status_label)

        status_layout.addStretch()

        # Active prompts counter
        self.active_prompts_label = QLabel("Active: 0/8")
        self.active_prompts_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(self.active_prompts_label)

        layout.addLayout(status_layout)

        # Prompt execution table
        self.orchestrator_table = QTableWidget(8, 4)
        self.orchestrator_table.setHorizontalHeaderLabels([
            "Prompt Type", "Status", "Confidence", "Last Run"
        ])

        # Configure table appearance
        header = self.orchestrator_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        self.orchestrator_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
                padding: 2px;
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 3px;
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
        """)

        self.orchestrator_table.verticalHeader().setVisible(False)
        self.orchestrator_table.setAlternatingRowColors(True)
        self.orchestrator_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orchestrator_table.setMaximumHeight(200)
        self.orchestrator_table.setMinimumHeight(200)

        # Initialize prompt types
        prompt_types = [
            "Emergency Response", "Position Management", "Profit Optimization",
            "Market Regime", "Risk Assessment", "Entry Timing",
            "Strategy Adaptation", "Opportunity Scanner"
        ]

        for row, prompt_type in enumerate(prompt_types):
            self.orchestrator_table.setItem(row, 0, QTableWidgetItem(prompt_type))
            self.orchestrator_table.setItem(row, 1, QTableWidgetItem("IDLE"))
            self.orchestrator_table.setItem(row, 2, QTableWidgetItem("--"))
            self.orchestrator_table.setItem(row, 3, QTableWidgetItem("Never"))

        layout.addWidget(self.orchestrator_table)

        # Control buttons
        controls_layout = QHBoxLayout()

        self.orchestrator_enable_btn = QPushButton("🚀 Enable Orchestrator")
        self.orchestrator_enable_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                padding: 5px 10px;
                border-radius: 3px;
            }}
        """)
        self.orchestrator_enable_btn.clicked.connect(self.toggle_orchestrator)
        controls_layout.addWidget(self.orchestrator_enable_btn)

        self.orchestrator_emergency_btn = QPushButton("🚨 Emergency Stop")
        self.orchestrator_emergency_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.WHITE};
                font-weight: bold;
                padding: 5px 10px;
                border-radius: 3px;
            }}
        """)
        self.orchestrator_emergency_btn.clicked.connect(self.emergency_stop_orchestrator)
        controls_layout.addWidget(self.orchestrator_emergency_btn)

        layout.addLayout(controls_layout)

        # Initialize orchestrator state
        self.orchestrator_enabled = False
        self.orchestrator_timer = QTimer()
        self.orchestrator_timer.timeout.connect(self.update_orchestrator_display)
        self.orchestrator_timer.start(5000)  # Update every 5 seconds

        return group

    def create_leverage_panel(self):
        """Create leverage analysis panel"""
        group = QGroupBox("Leverage Analysis")
        layout = QVBoxLayout(group)

        self.max_leverage_label = QLabel("Max Available: 1.0x")
        layout.addWidget(self.max_leverage_label)

        self.recommended_leverage_label = QLabel("Recommended: 1.0x")
        layout.addWidget(self.recommended_leverage_label)

        self.effective_leverage_label = QLabel("Effective: 0.4x")
        layout.addWidget(self.effective_leverage_label)

        self.position_size_label = QLabel("Position Size: 0.00 units ($0.00)")
        layout.addWidget(self.position_size_label)

        self.risk_per_trade_label = QLabel("Risk per Trade: $0.00")
        layout.addWidget(self.risk_per_trade_label)

        return group
    


    def create_market_analysis_panel(self):
        """Create enhanced market analysis panel with ScalperGPT enriched data"""
        group = QGroupBox("📊 ScalperGPT Market Intelligence")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Market data header
        market_header = QLabel("Enriched Market Data Analysis")
        market_header.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 3px;
            border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
        """)
        layout.addWidget(market_header)

        # Spread and latency metrics
        spread_layout = QHBoxLayout()

        self.scalper_spread_label = QLabel("Spread: $0.000000")
        self.scalper_spread_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        spread_layout.addWidget(self.scalper_spread_label)

        self.spread_pct_label = QLabel("(0.000%)")
        self.spread_pct_label.setStyleSheet(f"color: {MatrixTheme.TEXT}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        spread_layout.addWidget(self.spread_pct_label)

        layout.addLayout(spread_layout)

        # Tick analysis metrics
        tick_layout = QHBoxLayout()

        self.tick_atr_label = QLabel("Tick ATR: $0.000000")
        self.tick_atr_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        tick_layout.addWidget(self.tick_atr_label)

        self.trade_flow_label = QLabel("Flow: +0.0%")
        self.trade_flow_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        tick_layout.addWidget(self.trade_flow_label)

        layout.addLayout(tick_layout)

        # Volume momentum and latency
        momentum_layout = QHBoxLayout()

        self.volume_momentum_label = QLabel("Vol Momentum: +0.0%")
        self.volume_momentum_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        momentum_layout.addWidget(self.volume_momentum_label)

        self.data_latency_label = QLabel("Latency: 50ms")
        self.data_latency_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        momentum_layout.addWidget(self.data_latency_label)

        layout.addLayout(momentum_layout)

        # Open positions section
        positions_label = QLabel("📊 Open Positions:")
        positions_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px; margin-top: 5px;")
        layout.addWidget(positions_label)

        # Positions table
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(5)
        self.positions_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Size", "Entry", "PnL"
        ])

        # Apply Matrix theme to positions table
        self.positions_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-family: 'Courier New', monospace;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QTableWidget::item {{
                padding: 2px;
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.LIGHT_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 2px;
                border: 1px solid {MatrixTheme.GREEN};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
        """)

        # Configure table
        self.positions_table.verticalHeader().setVisible(False)
        self.positions_table.horizontalHeader().setStretchLastSection(True)
        self.positions_table.setAlternatingRowColors(True)
        self.positions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.positions_table.setMaximumHeight(120)
        self.positions_table.setMinimumHeight(120)

        layout.addWidget(self.positions_table)

        # Positions control buttons
        positions_buttons_layout = QHBoxLayout()

        self.refresh_positions_btn = QPushButton("🔄")
        self.refresh_positions_btn.clicked.connect(self.manual_refresh_positions)
        self.refresh_positions_btn.setMaximumWidth(30)
        self.refresh_positions_btn.setToolTip("Refresh positions manually")
        positions_buttons_layout.addWidget(self.refresh_positions_btn)

        self.close_selected_btn = QPushButton("❌")
        self.close_selected_btn.clicked.connect(self.close_selected_position)
        self.close_selected_btn.setMaximumWidth(30)
        self.close_selected_btn.setToolTip("Close selected position")
        positions_buttons_layout.addWidget(self.close_selected_btn)

        positions_buttons_layout.addStretch()

        # Positions status
        self.positions_status_label = QLabel("Positions: Loading...")
        self.positions_status_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 2px;
        """)
        positions_buttons_layout.addWidget(self.positions_status_label)

        layout.addLayout(positions_buttons_layout)

        # Initialize positions display
        self.refresh_open_positions()

        return group



    def create_risk_warnings_panel(self):
        """Create comprehensive risk warnings panel"""
        group = QGroupBox("Risk Management & Warnings")
        layout = QVBoxLayout(group)

        # Risk Metrics
        metrics_layout = QGridLayout()

        # Row 1: Portfolio Risk
        metrics_layout.addWidget(QLabel("Portfolio Risk:"), 0, 0)
        self.portfolio_risk_label = QLabel("2.5%")
        self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.portfolio_risk_label, 0, 1)

        # Row 2: Max Drawdown
        metrics_layout.addWidget(QLabel("Max Drawdown:"), 1, 0)
        self.max_drawdown_label = QLabel("5.2%")
        self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.max_drawdown_label, 1, 1)

        # Row 3: Correlation Risk
        metrics_layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_risk_label = QLabel("LOW")
        self.correlation_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.correlation_risk_label, 2, 1)

        # Row 4: Liquidity Risk
        metrics_layout.addWidget(QLabel("Liquidity Risk:"), 3, 0)
        self.liquidity_risk_label = QLabel("MEDIUM")
        self.liquidity_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.liquidity_risk_label, 3, 1)

        layout.addLayout(metrics_layout)

        # Active Warnings
        warnings_label = QLabel("Active Warnings:")
        warnings_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(warnings_label)

        self.risk_warnings_log = QTextEdit()
        self.risk_warnings_log.setReadOnly(True)
        self.risk_warnings_log.setMaximumHeight(100)

        # Apply Matrix theme scrollbar styling
        self.risk_warnings_log.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        # Add comprehensive sample warnings
        sample_warnings = [
            "⚠️ HIGH VOLATILITY: 24h volatility >5% - reduce position size",
            "⚠️ LOW LIQUIDITY: Order book depth <$50k - limit order size",
            "⚠️ CORRELATION ALERT: 0.85 correlation with BTC - diversify",
            "⚠️ LEVERAGE WARNING: Current 3.2x exceeds recommended 2.5x"
        ]

        for warning in sample_warnings:
            self.risk_warnings_log.append(warning)

        layout.addWidget(self.risk_warnings_log)

        return group

    def create_manual_trading_panel(self):
        """Create compact manual trading controls panel"""
        group = QGroupBox("Manual Trading")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Trading parameters - more compact
        params_layout = QGridLayout()
        params_layout.setSpacing(4)

        # Quantity - smaller spinbox
        params_layout.addWidget(QLabel("Quantity:"), 0, 0)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0.0001, 100000)
        self.quantity_spinbox.setDecimals(4)
        self.quantity_spinbox.setValue(50.0)
        self.quantity_spinbox.setMaximumHeight(25)  # Make more compact
        params_layout.addWidget(self.quantity_spinbox, 0, 1)

        # Leverage - smaller spinbox
        params_layout.addWidget(QLabel("Leverage:"), 1, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 125)
        self.leverage_spinbox.setValue(20)
        self.leverage_spinbox.setMaximumHeight(25)  # Make more compact
        # Connect leverage change to update trading interface
        self.leverage_spinbox.valueChanged.connect(self.on_leverage_changed)
        params_layout.addWidget(self.leverage_spinbox, 1, 1)

        # Compact Bid/Ask display
        params_layout.addWidget(QLabel("Best Bid:"), 2, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px;
                background-color: rgba(0, 255, 68, 0.1);
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_bid_label, 2, 1)

        params_layout.addWidget(QLabel("Best Ask:"), 3, 0)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.RED};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px;
                background-color: rgba(255, 0, 0, 0.1);
                border: 1px solid {MatrixTheme.RED};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_ask_label, 3, 1)

        # Compact spread display
        params_layout.addWidget(QLabel("Spread:"), 4, 0)
        self.spread_label = QLabel("--")
        self.spread_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                padding: 2px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                max-height: 18px;
            }}
        """)
        params_layout.addWidget(self.spread_label, 4, 1)

        # Price input for limit orders
        params_layout.addWidget(QLabel("Limit Price:"), 5, 0)
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.000001, 999999)
        self.price_spinbox.setDecimals(6)
        self.price_spinbox.setValue(0.175)
        self.price_spinbox.setToolTip("Price for limit orders (auto-filled from bid/ask)")
        params_layout.addWidget(self.price_spinbox, 5, 1)

        # Auto-fill price buttons
        price_buttons_layout = QHBoxLayout()

        fill_bid_btn = QPushButton("Use Bid")
        fill_bid_btn.setMaximumWidth(60)
        fill_bid_btn.clicked.connect(self.fill_bid_price)
        fill_bid_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        fill_ask_btn = QPushButton("Use Ask")
        fill_ask_btn.setMaximumWidth(60)
        fill_ask_btn.clicked.connect(self.fill_ask_price)
        fill_ask_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        price_buttons_layout.addWidget(fill_bid_btn)
        price_buttons_layout.addWidget(fill_ask_btn)
        price_buttons_layout.addStretch()

        params_layout.addLayout(price_buttons_layout, 6, 0, 1, 2)

        # Trading mode status (removed duplicate - mode is already shown in menu bar)

        # Initialize bid/ask tracking
        self.current_bid = None
        self.current_ask = None
        self.last_bid = None
        self.last_ask = None

        layout.addLayout(params_layout)

        # Compact trading buttons
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(4)

        # Long buttons (green) - more compact
        self.limit_long_btn = QPushButton("LIMIT LONG")
        self.limit_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #006600;
                color: white;
                font-weight: bold;
                padding: 4px;
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.limit_long_btn.clicked.connect(self.place_limit_long)
        buttons_layout.addWidget(self.limit_long_btn, 0, 0)

        self.market_long_btn = QPushButton("MARKET LONG")
        self.market_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #009900;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.market_long_btn.clicked.connect(self.place_market_long)
        buttons_layout.addWidget(self.market_long_btn, 0, 1)

        # Short buttons (red)
        self.limit_short_btn = QPushButton("LIMIT SHORT")
        self.limit_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #660000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.limit_short_btn.clicked.connect(self.place_limit_short)
        buttons_layout.addWidget(self.limit_short_btn, 1, 0)

        self.market_short_btn = QPushButton("MARKET SHORT")
        self.market_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #990000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.market_short_btn.clicked.connect(self.place_market_short)
        buttons_layout.addWidget(self.market_short_btn, 1, 1)

        # Control buttons
        self.close_all_btn = QPushButton("CLOSE ALL")
        self.close_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ff6600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.YELLOW};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.YELLOW};
                color: black;
            }}
        """)
        self.close_all_btn.clicked.connect(self.close_all_positions)
        buttons_layout.addWidget(self.close_all_btn, 2, 0)

        self.cancel_all_btn = QPushButton("CANCEL ALL")
        self.cancel_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #666666;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GRAY};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GRAY};
                color: white;
            }}
        """)
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders)
        buttons_layout.addWidget(self.cancel_all_btn, 2, 1)

        layout.addLayout(buttons_layout)

        return group

    def create_open_orders_panel(self):
        """Create open orders panel using me2_stable.py functions"""
        group = QGroupBox("📋 Open Orders")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(5, 2, 5, 5)
        layout.setSpacing(4)

        # Orders table
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(6)
        self.orders_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Type", "Amount", "Price", "Status"
        ])

        # Apply Matrix theme to table
        self.orders_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-family: 'Courier New', monospace;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            }}
            QTableWidget::item {{
                padding: 4px;
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.LIGHT_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 4px;
                border: 1px solid {MatrixTheme.GREEN};
                font-weight: bold;
            }}
        """)

        # Hide vertical header and configure columns
        self.orders_table.verticalHeader().setVisible(False)
        self.orders_table.horizontalHeader().setStretchLastSection(True)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setMinimumHeight(200)

        layout.addWidget(self.orders_table)

        # Control buttons
        buttons_layout = QHBoxLayout()

        self.refresh_orders_btn = QPushButton("🔄 Refresh")
        self.refresh_orders_btn.clicked.connect(self.refresh_open_orders)
        self.refresh_orders_btn.setMaximumWidth(100)
        buttons_layout.addWidget(self.refresh_orders_btn)

        self.cancel_selected_btn = QPushButton("❌ Cancel Selected")
        self.cancel_selected_btn.clicked.connect(self.cancel_selected_order)
        self.cancel_selected_btn.setMaximumWidth(150)
        buttons_layout.addWidget(self.cancel_selected_btn)

        self.cancel_all_btn = QPushButton("🗑️ Cancel All")
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders_confirm)
        self.cancel_all_btn.setMaximumWidth(120)
        buttons_layout.addWidget(self.cancel_all_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Status label
        self.orders_status_label = QLabel("Orders: Loading...")
        self.orders_status_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 2px;
        """)
        layout.addWidget(self.orders_status_label)

        # Initialize orders display
        self.refresh_open_orders()

        # Initialize other components that were in the chart panel
        self.setup_live_data_manager()
        self.setup_real_trading_interface()
        self.setup_lmstudio_runner()
        self.setup_signal_trading_engine()
        self.setup_session_management()

        return group

    def refresh_open_orders(self):
        """Refresh open orders display using direct CCXT"""
        try:
            # Fetch open orders using direct CCXT only
            current_symbol = self.symbol_combo.currentText()
            orders = fetch_open_orders(current_symbol)

            # Update table
            self.orders_table.setRowCount(len(orders))

            for row, order in enumerate(orders):
                # Extract order information
                symbol = order.get('symbol', 'N/A')
                side = order.get('side', 'N/A')
                order_type = order.get('type', 'N/A')
                amount = order.get('amount', 0)
                price = order.get('price', 0)
                status = order.get('status', 'N/A')

                # Populate table cells
                self.orders_table.setItem(row, 0, QTableWidgetItem(str(symbol)))

                # Color-code side
                side_item = QTableWidgetItem(str(side))
                if side.upper() == 'BUY':
                    side_item.setForeground(QColor(MatrixTheme.LIGHT_GREEN))
                elif side.upper() == 'SELL':
                    side_item.setForeground(QColor(MatrixTheme.RED))
                self.orders_table.setItem(row, 1, side_item)

                self.orders_table.setItem(row, 2, QTableWidgetItem(str(order_type)))
                self.orders_table.setItem(row, 3, QTableWidgetItem(f"{amount:.4f}"))
                self.orders_table.setItem(row, 4, QTableWidgetItem(f"{price:.6f}"))

                # Color-code status
                status_item = QTableWidgetItem(str(status))
                if status.lower() == 'open':
                    status_item.setForeground(QColor(MatrixTheme.YELLOW))
                elif status.lower() == 'filled':
                    status_item.setForeground(QColor(MatrixTheme.GREEN))
                elif status.lower() == 'cancelled':
                    status_item.setForeground(QColor(MatrixTheme.RED))
                self.orders_table.setItem(row, 5, status_item)

            # Update status
            self.orders_status_label.setText(f"Orders: {len(orders)} open")
            self.log_message(f"📋 Refreshed {len(orders)} open orders")

        except Exception as e:
            self.log_message(f"❌ Error refreshing orders: {e}")
            self.orders_status_label.setText("Orders: Error loading")

    def cancel_selected_order(self):
        """Cancel selected order"""
        try:
            current_row = self.orders_table.currentRow()
            if current_row < 0:
                self.log_message("⚠️ No order selected")
                return

            # Get order details
            symbol = self.orders_table.item(current_row, 0).text()
            side = self.orders_table.item(current_row, 1).text()
            amount = self.orders_table.item(current_row, 3).text()

            # Confirm cancellation
            reply = QMessageBox.question(
                self,
                "Cancel Order",
                f"Cancel {side} order for {amount} {symbol}?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # TODO: Implement actual order cancellation
                self.log_message(f"🗑️ Cancelling order: {side} {amount} {symbol}")
                # Refresh after cancellation
                QTimer.singleShot(1000, self.refresh_open_orders)

        except Exception as e:
            self.log_message(f"❌ Error cancelling order: {e}")

    def cancel_all_orders_confirm(self):
        """Confirm and cancel all orders"""
        try:
            orders_count = self.orders_table.rowCount()
            if orders_count == 0:
                self.log_message("⚠️ No orders to cancel")
                return


            reply = QMessageBox.question(
                self,
                "Cancel All Orders",
                f"Cancel all {orders_count} open orders?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # TODO: Implement actual bulk order cancellation
                self.log_message(f"🗑️ Cancelling all {orders_count} orders")
                # Refresh after cancellation
                QTimer.singleShot(1000, self.refresh_open_orders)

        except Exception as e:
            self.log_message(f"❌ Error cancelling all orders: {e}")

    def refresh_open_positions(self):
        """Refresh open positions display using direct CCXT"""
        try:
            # Fetch ALL open positions using direct CCXT only
            positions = fetch_open_positions()
            current_symbol = self.symbol_combo.currentText()

            # Check if position count changed for automatic updates
            if len(positions) != self.last_position_count:
                self.last_position_count = len(positions)
                if len(positions) > 0:
                    self.log_message(f"🔔 Position change detected: {len(positions)} open positions")

            # Update table
            self.positions_table.setRowCount(len(positions))

            for row, position in enumerate(positions):
                # Extract position information
                symbol = position.get('symbol', 'N/A')
                side = position.get('side', 'N/A')
                contracts = position.get('contracts', 0)
                entry_price = position.get('entryPrice', 0)
                unrealized_pnl = position.get('unrealizedPnl', 0)

                # Populate table cells
                self.positions_table.setItem(row, 0, QTableWidgetItem(str(symbol)))

                # Color-code side
                side_item = QTableWidgetItem(str(side).upper())
                if side.upper() == 'LONG':
                    side_item.setForeground(QColor(MatrixTheme.LIGHT_GREEN))
                elif side.upper() == 'SHORT':
                    side_item.setForeground(QColor(MatrixTheme.RED))
                self.positions_table.setItem(row, 1, side_item)

                self.positions_table.setItem(row, 2, QTableWidgetItem(f"{contracts:.4f}"))
                self.positions_table.setItem(row, 3, QTableWidgetItem(f"{entry_price:.6f}"))

                # Color-code PnL
                pnl_item = QTableWidgetItem(f"{unrealized_pnl:.2f}")
                if unrealized_pnl > 0:
                    pnl_item.setForeground(QColor(MatrixTheme.GREEN))
                elif unrealized_pnl < 0:
                    pnl_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    pnl_item.setForeground(QColor(MatrixTheme.YELLOW))
                self.positions_table.setItem(row, 4, pnl_item)

            # Update status with more detail
            current_symbol_positions = [p for p in positions if p.get('symbol') == current_symbol]
            if current_symbol_positions:
                self.positions_status_label.setText(f"Positions: {len(positions)} total ({len(current_symbol_positions)} for {current_symbol})")
            else:
                self.positions_status_label.setText(f"Positions: {len(positions)} total (0 for {current_symbol})")

            # Only log if there are positions or if this is a manual refresh
            if len(positions) > 0 or hasattr(self, '_manual_refresh'):
                self.log_message(f"📊 Refreshed {len(positions)} open positions")
                if hasattr(self, '_manual_refresh'):
                    delattr(self, '_manual_refresh')

        except Exception as e:
            self.log_message(f"❌ Error refreshing positions: {e}")
            self.positions_status_label.setText("Positions: Error loading")

    def manual_refresh_positions(self):
        """Manual refresh of positions with logging"""
        self._manual_refresh = True
        self.refresh_open_positions()

    def close_selected_position(self):
        """Close selected position"""
        try:
            current_row = self.positions_table.currentRow()
            if current_row < 0:
                self.log_message("⚠️ No position selected")
                return

            # Get position details
            symbol = self.positions_table.item(current_row, 0).text()
            side = self.positions_table.item(current_row, 1).text()
            size = self.positions_table.item(current_row, 2).text()

            # Confirm closure
            reply = QMessageBox.question(
                self,
                "Close Position",
                f"Close {side} position of {size} {symbol}?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # TODO: Implement actual position closure
                self.log_message(f"🔒 Closing position: {side} {size} {symbol}")
                # Refresh after closure
                QTimer.singleShot(1000, self.refresh_open_positions)

        except Exception as e:
            self.log_message(f"❌ Error closing position: {e}")

    def refresh_orders_and_positions(self):
        """Refresh both orders and positions automatically"""
        try:
            # Only refresh if the widgets exist
            if hasattr(self, 'orders_table'):
                self.refresh_open_orders()
            if hasattr(self, 'positions_table'):
                self.refresh_open_positions()
        except Exception as e:
            # Silent error handling for background refresh
            pass

    def create_fallback_chart(self):
        """Create fallback chart if live chart widget is not available"""
        # Chart controls
        controls_layout = QHBoxLayout()

        # Timeframe selector
        controls_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.timeframe_combo)

        # Chart type selector
        controls_layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Line", "Candlestick"])
        self.chart_type_combo.setCurrentText("Line")
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.chart_type_combo)

        controls_layout.addStretch()

        fallback_widget = QWidget()
        fallback_layout = QVBoxLayout(fallback_widget)
        fallback_layout.addLayout(controls_layout)

        # Create PyQtGraph chart
        self.chart_widget = pg.PlotWidget(
            background='#000000',
            enableMenu=False
        )
        self.chart_widget.setMinimumHeight(300)
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        self.chart_widget.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)

        # Disable crosshair and auto-range to prevent zoom issues
        self.chart_widget.getPlotItem().getViewBox().setMouseEnabled(x=True, y=True)
        self.chart_widget.getPlotItem().enableAutoRange(enable=True)
        self.chart_widget.getPlotItem().setAutoVisible(y=True)

        # Connect chart click event for order placement
        self.chart_widget.scene().sigMouseClicked.connect(self.on_chart_click)

        fallback_layout.addWidget(self.chart_widget)

        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY order | Right-click: SELL order")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px; padding: 5px;")
        fallback_layout.addWidget(instructions)

        # Initialize chart with sample data
        self.update_chart()

        return fallback_widget

    def setup_live_data_manager(self):
        """Setup live data manager for real-time chart updates"""
        try:
            # Check if already initialized to prevent multiple instances
            if hasattr(self, 'live_data_manager') and self.live_data_manager is not None:
                print("✓ Live data manager already initialized")
                return

            from data.live_data_manager import LiveDataManager

            # Create live data manager
            self.live_data_manager = LiveDataManager("htx")

            # Connect signals
            self.live_data_manager.chart_data_updated.connect(self.on_live_chart_data_updated)
            self.live_data_manager.price_updated.connect(self.on_live_price_updated)
            self.live_data_manager.orderbook_updated.connect(self.on_live_orderbook_updated)
            self.live_data_manager.connection_status_changed.connect(self.on_live_connection_status)

            # Connect WebSocket trade updates directly to chart
            self.live_data_manager.ws_client.trade_update.connect(self.on_trade_update)

            # Subscribe to current symbol
            current_symbol = self.symbol_combo.currentText()
            self.live_data_manager.subscribe_symbol(current_symbol, ["1m", "5m", "15m"])

            # Connect to live data
            self.live_data_manager.connect()

            print("✓ Live data manager initialized")

        except ImportError as e:
            print(f"Could not import LiveDataManager: {e}")
            self.live_data_manager = None

    def setup_real_trading_interface(self):
        """Setup real trading interface for actual order execution"""
        try:
            from trading.real_trading_interface import RealTradingInterface
            from ml.prediction_accuracy_tracker import PredictionAccuracyTracker

            # Create real trading interface with production settings
            # Force live trading mode (demo_mode=False) for production
            production_demo_mode = False  # Always use live trading in production
            print(f"🔧 Initializing RealTradingInterface with demo_mode={production_demo_mode}")
            self.real_trading = RealTradingInterface("htx", demo_mode=production_demo_mode)

            # Create prediction accuracy tracker
            self.prediction_tracker = PredictionAccuracyTracker(evaluation_window_minutes=5)

            # Connect signals
            self.real_trading.order_status_updated.connect(self.on_order_status_updated)
            self.real_trading.position_status_updated.connect(self.on_position_status_updated)
            self.real_trading.balance_status_updated.connect(self.on_balance_status_updated)
            self.real_trading.trading_error.connect(self.on_trading_error)
            self.real_trading.trading_status.connect(self.on_trading_status)
            self.real_trading.pnl_updated.connect(self.on_pnl_updated)
            self.real_trading.risk_warning.connect(self.on_risk_warning)

            # Set current symbol
            current_symbol = self.symbol_combo.currentText()
            self.real_trading.set_current_symbol(current_symbol)

            # Initial balance fetch for menu bar display
            self.update_balance_display()

            print("✓ Real trading interface initialized")

        except ImportError as e:
            print(f"Could not import RealTradingInterface: {e}")
            self.real_trading = None

    def setup_signal_trading_engine(self):
        """Setup signal trading engine for automated trading"""
        try:
            from trading.signal_trading_engine import SignalTradingEngine

            if hasattr(self, 'real_trading') and self.real_trading:
                # Create signal trading engine
                self.signal_trading = SignalTradingEngine(self.real_trading)

                # Connect signals
                self.signal_trading.signal_received.connect(self.on_signal_received)
                self.signal_trading.trade_decision_made.connect(self.on_trade_decision_made)
                self.signal_trading.automated_trade_executed.connect(self.on_automated_trade_executed)
                self.signal_trading.risk_limit_triggered.connect(self.on_risk_limit_triggered)
                self.signal_trading.engine_status_changed.connect(self.on_engine_status_changed)

                print("✓ Signal trading engine initialized")
            else:
                print("⚠ Real trading interface not available for signal trading")
                self.signal_trading = None

        except ImportError as e:
            print(f"Could not import SignalTradingEngine: {e}")
            self.signal_trading = None

    def setup_session_management(self):
        """Setup session management and persistence"""
        try:
            from storage.database_manager import DatabaseManager
            from storage.session_manager import SessionManager, TradeRecorder

            # Create database manager
            self.db_manager = DatabaseManager()

            # Create session manager
            self.session_manager = SessionManager(self.db_manager)

            # Create trade recorder
            self.trade_recorder = TradeRecorder(self.session_manager)

            # Connect signals
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
            self.session_manager.trade_recorded.connect(self.on_trade_recorded_to_db)
            self.session_manager.signal_recorded.connect(self.on_signal_recorded_to_db)

            # Start a session automatically in LIVE mode
            current_symbol = self.symbol_combo.currentText()
            session_id = self.session_manager.start_session(
                mode="live",  # LIVE TRADING MODE
                symbol=current_symbol,
                initial_balance=1000.0,  # Real account balance will be fetched from exchange
                configuration={
                    "leverage": self.leverage_spinbox.value(),
                    "base_position_size": self.quantity_spinbox.value(),
                    "auto_trading": False,
                    "live_trading": True,  # Enable live trading
                    "exchange": "htx",
                    "api_credentials": True
                }
            )

            print("✓ Session management initialized")
            print(f"✓ Started session: {session_id}")

        except ImportError as e:
            print(f"Could not import session management: {e}")
            self.session_manager = None
            self.trade_recorder = None

    def setup_timers(self):
        """Setup optimized update timers with intelligent scheduling"""

        # 🚀 PERFORMANCE FIX 1: Consolidated Master Timer
        # Single timer that coordinates all updates to prevent timer conflicts
        self.master_timer = QTimer()
        self.master_timer.timeout.connect(self.master_update_cycle)
        self.master_timer.start(1000)  # 1-second master cycle

        # Update counters for intelligent scheduling
        self.update_counter = 0
        self.last_position_count = 0
        self.last_order_count = 0

        # 🚀 PERFORMANCE FIX 2: Optimized Thread Pool Management
        # Separate thread pools for different operation types to prevent saturation
        from PyQt5.QtCore import QThreadPool
        import os

        # Calculate optimal thread counts based on CPU cores
        cpu_cores = os.cpu_count() or 4

        # Main thread pool for GUI operations (positions, orders, charts)
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(max(2, cpu_cores // 2))  # Reserve half cores for GUI

        # 🚀 NEW: Dedicated LLM thread pool to prevent UI blocking
        self.llm_thread_pool = QThreadPool()
        self.llm_thread_pool.setMaxThreadCount(1)  # Single thread for sequential LLM execution

        self.log_message(f"🧵 Thread pools initialized: GUI={self.thread_pool.maxThreadCount()}, LLM={self.llm_thread_pool.maxThreadCount()}, CPU cores={cpu_cores}")

        # 🚀 PERFORMANCE FIX 3: Update Scheduling Map
        # Intelligent update scheduling to prevent simultaneous heavy operations
        self.update_schedule = {
            'time_display': 1,      # Every 1 second (lightweight)
            'bid_ask': 3,           # Every 3 seconds
            'positions_orders': 5,  # Every 5 seconds (heavy operation)
            'balance': 30,          # Every 30 seconds
            'chart': 15,            # Every 15 seconds (very heavy)
        }

        # 🚀 PERFORMANCE FIX 4: Cache System
        # Cache frequently accessed data to reduce API calls
        self.data_cache = {
            'positions': {'data': [], 'timestamp': 0, 'ttl': 5},
            'orders': {'data': [], 'timestamp': 0, 'ttl': 5},
            'balance': {'data': {}, 'timestamp': 0, 'ttl': 30},
            'market_data': {'data': {}, 'timestamp': 0, 'ttl': 10}
        }

        # Timer system initialized (reduced logging)

        # 🚀 PERFORMANCE FIX 5: Database I/O Optimization
        # Move database operations off main thread
        self.db_write_queue = []
        self.db_worker_timer = QTimer()
        self.db_worker_timer.timeout.connect(self.flush_database_writes)
        self.db_worker_timer.start(5000)  # Flush every 5 seconds

        # 🚀 PERFORMANCE FIX 6: Memory Management
        # Periodic cleanup to prevent memory leaks
        self.memory_cleanup_timer = QTimer()
        self.memory_cleanup_timer.timeout.connect(self.cleanup_memory)
        self.memory_cleanup_timer.start(60000)  # Cleanup every minute

        # 🚀 PERFORMANCE FIX 7: Performance Monitoring
        # Track performance metrics
        self.performance_metrics = {
            'gui_updates_per_second': 0,
            'api_calls_per_minute': 0,
            'memory_usage_mb': 0,
            'thread_pool_usage': 0,
            'last_lag_warning': 0
        }

    def master_update_cycle(self):
        """🚀 PERFORMANCE FIX: Master update cycle that intelligently schedules all updates"""
        try:
            self.update_counter += 1

            # Always update time (lightweight)
            if self.update_counter % self.update_schedule['time_display'] == 0:
                self.update_time()

            # Bid/Ask updates (medium weight)
            if self.update_counter % self.update_schedule['bid_ask'] == 0:
                self.schedule_background_task('bid_ask', self.update_bid_ask_display_async)

            # Positions/Orders updates (heavy weight) - staggered to avoid conflicts
            if self.update_counter % self.update_schedule['positions_orders'] == 0:
                self.schedule_background_task('positions_orders', self.refresh_orders_and_positions_async)

            # Balance updates (medium weight)
            if self.update_counter % self.update_schedule['balance'] == 0:
                self.schedule_background_task('balance', self.update_balance_display_async)

            # Chart updates (very heavy) - least frequent
            if self.update_counter % self.update_schedule['chart'] == 0:
                self.schedule_background_task('chart', self.update_chart_async)

            # 🚀 PERFORMANCE FIX: Performance monitoring every 30 seconds
            if self.update_counter % 30 == 0:
                self.monitor_performance()

            # 🚀 PERFORMANCE FIX: Adaptive optimization every 60 seconds
            if self.update_counter % 60 == 0:
                self.optimize_for_performance()

            # Reset counter to prevent overflow
            if self.update_counter >= 300:  # 5 minutes
                self.update_counter = 0

        except Exception as e:
            print(f"Error in master update cycle: {e}")

    def schedule_background_task(self, task_name, task_function):
        """🚀 PERFORMANCE FIX: Schedule task in background thread to prevent UI blocking"""
        try:
            # Check if we have available threads
            if self.thread_pool.activeThreadCount() < self.thread_pool.maxThreadCount():
                worker = BackgroundTaskWorker(task_name, task_function)
                worker.signals.finished.connect(self.on_background_task_finished)
                worker.signals.error.connect(self.on_background_task_error)
                self.thread_pool.start(worker)
            else:
                # Skip this update if thread pool is saturated
                print(f"⚠️ Skipping {task_name} update - thread pool saturated")
        except Exception as e:
            print(f"Error scheduling background task {task_name}: {e}")

    def on_background_task_finished(self, task_name, result):
        """Handle completed background task"""
        try:
            # Update UI with results from background thread
            if task_name == 'positions_orders' and result:
                self.update_positions_orders_ui(result)
            elif task_name == 'balance' and result:
                self.update_balance_ui(result)
            elif task_name == 'bid_ask' and result:
                self.update_bid_ask_ui(result)
        except Exception as e:
            print(f"Error handling background task result: {e}")

    def on_background_task_error(self, task_name, error):
        """Handle background task errors"""
        print(f"Background task {task_name} error: {error}")

    # 🚀 PERFORMANCE FIX: Async methods for background execution
    def refresh_orders_and_positions_async(self):
        """Async version of orders/positions refresh - runs in background thread"""
        try:
            import time
            start_time = time.time()

            # Check cache first
            positions_cache = self.data_cache['positions']
            orders_cache = self.data_cache['orders']
            current_time = time.time()

            result = {}

            # Fetch positions if cache expired
            if current_time - positions_cache['timestamp'] > positions_cache['ttl']:
                positions = fetch_open_positions()
                positions_cache['data'] = positions
                positions_cache['timestamp'] = current_time
                result['positions'] = positions
            else:
                result['positions'] = positions_cache['data']

            # Fetch orders if cache expired
            if current_time - orders_cache['timestamp'] > orders_cache['ttl']:
                current_symbol = getattr(self, 'current_symbol', 'DOGE/USDT:USDT')
                orders = fetch_open_orders(current_symbol)
                orders_cache['data'] = orders
                orders_cache['timestamp'] = current_time
                result['orders'] = orders
            else:
                result['orders'] = orders_cache['data']

            return result

        except Exception as e:
            print(f"Error in async orders/positions refresh: {e}")
            return None

    def update_bid_ask_display_async(self):
        """Async version of bid/ask update - runs in background thread"""
        try:
            # Get cached market data or fetch new
            market_cache = self.data_cache['market_data']
            current_time = time.time()

            if current_time - market_cache['timestamp'] > market_cache['ttl']:
                # Simulate bid/ask fetch (replace with actual WebSocket data)
                current_price = getattr(self, 'current_bid', 0.17) or 0.17
                result = {
                    'bid': current_price * 0.9995,
                    'ask': current_price * 1.0005,
                    'timestamp': current_time
                }
                market_cache['data'] = result
                market_cache['timestamp'] = current_time
                return result
            else:
                return market_cache['data']

        except Exception as e:
            print(f"Error in async bid/ask update: {e}")
            return None

    def update_balance_display_async(self):
        """Async version of balance update - runs in background thread"""
        try:
            # Check cache first
            balance_cache = self.data_cache['balance']
            current_time = time.time()

            if current_time - balance_cache['timestamp'] > balance_cache['ttl']:
                # Fetch balance from real trading interface
                if hasattr(self, 'real_trading') and self.real_trading:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info and 'USDT' in balance_info:
                        result = {
                            'equity': balance_info['USDT'].get('total', 0),
                            'free': balance_info['USDT'].get('free', 0),
                            'timestamp': current_time
                        }
                        balance_cache['data'] = result
                        balance_cache['timestamp'] = current_time
                        return result
            else:
                return balance_cache['data']

        except Exception as e:
            print(f"Error in async balance update: {e}")
            return None

    def update_chart_async(self):
        """Async version of chart update - runs in background thread"""
        try:
            # Lightweight chart data preparation
            # Actual chart rendering will happen on main thread
            current_symbol = getattr(self, 'current_symbol', 'DOGE/USDT:USDT')
            timeframe = getattr(self, 'current_timeframe', '1m')

            # Return minimal data for chart update
            return {
                'symbol': current_symbol,
                'timeframe': timeframe,
                'timestamp': time.time(),
                'update_needed': True
            }

        except Exception as e:
            print(f"Error in async chart update: {e}")
            return None

    # 🚀 PERFORMANCE FIX: Optimized UI update methods
    def update_positions_orders_ui(self, data):
        """Update positions and orders UI with background-fetched data"""
        try:
            if not data:
                return

            positions = data.get('positions', [])
            orders = data.get('orders', [])

            # Update positions table efficiently
            if hasattr(self, 'positions_table') and positions is not None:
                current_row_count = self.positions_table.rowCount()
                new_row_count = len(positions)

                # Only update if data changed
                if new_row_count != current_row_count or new_row_count != self.last_position_count:
                    self.positions_table.setRowCount(new_row_count)

                    for row, position in enumerate(positions):
                        # Use batch updates for better performance
                        symbol = position.get('symbol', 'N/A')
                        side = position.get('side', 'N/A')
                        size = position.get('size', 0)
                        entry_price = position.get('entryPrice', 0)
                        pnl = position.get('unrealizedPnl', 0)

                        # Batch GUI updates
                        self.batch_gui_update(f'pos_symbol_{row}', 'text', symbol)
                        self.batch_gui_update(f'pos_side_{row}', 'text', side.upper())
                        self.batch_gui_update(f'pos_size_{row}', 'text', f"{size:.4f}")
                        self.batch_gui_update(f'pos_price_{row}', 'text', f"{entry_price:.6f}")
                        self.batch_gui_update(f'pos_pnl_{row}', 'text', f"{pnl:.2f}")

                    self.last_position_count = new_row_count

            # Update orders table efficiently
            if hasattr(self, 'orders_table') and orders is not None:
                current_row_count = self.orders_table.rowCount()
                new_row_count = len(orders)

                # Only update if data changed
                if new_row_count != current_row_count or new_row_count != self.last_order_count:
                    self.orders_table.setRowCount(new_row_count)

                    for row, order in enumerate(orders):
                        symbol = order.get('symbol', 'N/A')
                        side = order.get('side', 'N/A')
                        amount = order.get('amount', 0)
                        price = order.get('price', 0)
                        order_type = order.get('type', 'N/A')

                        # Batch GUI updates
                        self.batch_gui_update(f'ord_symbol_{row}', 'text', symbol)
                        self.batch_gui_update(f'ord_side_{row}', 'text', side.upper())
                        self.batch_gui_update(f'ord_amount_{row}', 'text', f"{amount:.4f}")
                        self.batch_gui_update(f'ord_price_{row}', 'text', f"{price:.6f}")
                        self.batch_gui_update(f'ord_type_{row}', 'text', order_type.upper())

                    self.last_order_count = new_row_count

        except Exception as e:
            print(f"Error updating positions/orders UI: {e}")

    def update_balance_ui(self, data):
        """Update balance UI with background-fetched data"""
        try:
            if not data:
                return

            equity = data.get('equity', 0)
            free = data.get('free', 0)

            # Use batched updates for better performance
            balance_text = f"Equity: ${equity:.2f} Free: ${free:.2f}"
            self.batch_gui_update('balance_label', 'text', balance_text)

        except Exception as e:
            print(f"Error updating balance UI: {e}")

    def update_bid_ask_ui(self, data):
        """Update bid/ask UI with background-fetched data"""
        try:
            if not data:
                return

            bid = data.get('bid', 0)
            ask = data.get('ask', 0)

            # Use batched updates for better performance
            if hasattr(self, 'best_bid_label'):
                self.batch_gui_update('best_bid_label', 'text', f"{bid:.6f}")
            if hasattr(self, 'best_ask_label'):
                self.batch_gui_update('best_ask_label', 'text', f"{ask:.6f}")

        except Exception as e:
            print(f"Error updating bid/ask UI: {e}")

    # 🚀 PERFORMANCE FIX: Database I/O Optimization
    def queue_database_write(self, table, data):
        """Queue database write operation for background processing"""
        try:
            self.db_write_queue.append({
                'table': table,
                'data': data,
                'timestamp': time.time()
            })

            # Prevent queue from growing too large
            if len(self.db_write_queue) > 1000:
                self.flush_database_writes()

        except Exception as e:
            print(f"Error queuing database write: {e}")

    def flush_database_writes(self):
        """Flush queued database writes in background thread"""
        try:
            if not self.db_write_queue:
                return

            # Move writes to background thread
            writes_to_process = self.db_write_queue.copy()
            self.db_write_queue.clear()

            # Schedule background database operation
            self.schedule_background_task('database_writes',
                                        lambda: self.process_database_writes(writes_to_process))

        except Exception as e:
            print(f"Error flushing database writes: {e}")

    def process_database_writes(self, writes):
        """Process database writes in background thread"""
        try:
            import sqlite3
            import os

            # Connect to database
            db_path = 'trading_data.db'
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            writes_processed = 0
            for write_op in writes:
                try:
                    table = write_op['table']
                    data = write_op['data']

                    if table == 'trades':
                        cursor.execute("""
                            INSERT OR REPLACE INTO trades
                            (timestamp, symbol, side, amount, price, pnl)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (data.get('timestamp'), data.get('symbol'),
                             data.get('side'), data.get('amount'),
                             data.get('price'), data.get('pnl')))
                    elif table == 'market_data':
                        cursor.execute("""
                            INSERT OR REPLACE INTO market_data
                            (timestamp, symbol, price, volume)
                            VALUES (?, ?, ?, ?)
                        """, (data.get('timestamp'), data.get('symbol'),
                             data.get('price'), data.get('volume')))

                    writes_processed += 1

                except Exception as write_error:
                    print(f"Error processing individual write: {write_error}")

            # Commit all writes at once
            conn.commit()
            conn.close()

            # Only log if significant number of writes
            if writes_processed > 10:
                print(f"📊 Processed {writes_processed} database writes")
            return writes_processed

        except Exception as e:
            print(f"Error processing database writes: {e}")
            return 0

    # 🚀 PERFORMANCE FIX: Memory Management
    def cleanup_memory(self):
        """Periodic memory cleanup to prevent leaks"""
        try:
            import gc
            import psutil
            import os

            # Force garbage collection
            collected = gc.collect()

            # Get memory usage
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024

            # Update performance metrics
            self.performance_metrics['memory_usage_mb'] = memory_mb

            # Clean up old cache entries
            current_time = time.time()
            for cache_name, cache_data in self.data_cache.items():
                if current_time - cache_data['timestamp'] > cache_data['ttl'] * 2:
                    cache_data['data'] = {} if isinstance(cache_data['data'], dict) else []
                    cache_data['timestamp'] = 0

            # Clean up old historical data
            if len(self.historical_verdicts) > 100:
                self.historical_verdicts = self.historical_verdicts[-50:]

            # Log memory cleanup only if very significant
            if collected > 500 or memory_mb > 1000:
                print(f"🧹 Memory cleanup: {collected} objects collected, {memory_mb:.1f}MB used")

        except Exception as e:
            print(f"Error in memory cleanup: {e}")

    # 🚀 PERFORMANCE FIX: Performance Monitoring System
    def monitor_performance(self):
        """Monitor system performance and alert on lag-causing operations"""
        try:
            import psutil
            import os

            # Get current performance metrics
            process = psutil.Process(os.getpid())
            cpu_percent = process.cpu_percent()
            memory_mb = process.memory_info().rss / 1024 / 1024
            thread_count = process.num_threads()

            # Update performance metrics with enhanced thread monitoring
            gui_pool_usage = self.thread_pool.activeThreadCount() if hasattr(self, 'thread_pool') else 0
            llm_pool_usage = self.llm_thread_pool.activeThreadCount() if hasattr(self, 'llm_thread_pool') else 0

            self.performance_metrics.update({
                'cpu_percent': cpu_percent,
                'memory_usage_mb': memory_mb,
                'thread_count': thread_count,
                'gui_thread_pool_usage': gui_pool_usage,
                'llm_thread_pool_usage': llm_pool_usage,
                'total_thread_pool_usage': gui_pool_usage + llm_pool_usage
            })

            # Performance alerts
            current_time = time.time()

            # High CPU usage alert
            if cpu_percent > 80:
                if current_time - self.performance_metrics.get('last_cpu_warning', 0) > 30:
                    print(f"⚠️ HIGH CPU USAGE: {cpu_percent:.1f}% - Consider reducing update frequencies")
                    self.performance_metrics['last_cpu_warning'] = current_time

            # High memory usage alert
            if memory_mb > 1000:  # 1GB
                if current_time - self.performance_metrics.get('last_memory_warning', 0) > 60:
                    print(f"⚠️ HIGH MEMORY USAGE: {memory_mb:.1f}MB - Running cleanup...")
                    self.cleanup_memory()
                    self.performance_metrics['last_memory_warning'] = current_time

            # 🚀 ENHANCED: Thread pool saturation monitoring for both pools
            if hasattr(self, 'thread_pool') and hasattr(self, 'llm_thread_pool'):
                gui_pool_usage = self.thread_pool.activeThreadCount() / self.thread_pool.maxThreadCount()
                llm_pool_usage = self.llm_thread_pool.activeThreadCount() / self.llm_thread_pool.maxThreadCount()

                # Alert on GUI thread pool saturation
                if gui_pool_usage > 0.8:
                    if current_time - self.performance_metrics.get('last_gui_thread_warning', 0) > 15:
                        self.log_message(f"⚠️ GUI THREAD POOL SATURATED: {gui_pool_usage:.1%} ({self.thread_pool.activeThreadCount()}/{self.thread_pool.maxThreadCount()}) - Skipping non-critical updates")
                        self.performance_metrics['last_gui_thread_warning'] = current_time

                # Alert on LLM thread pool usage (should be 0 or 1 for sequential execution)
                if llm_pool_usage > 0:
                    if current_time - self.performance_metrics.get('last_llm_thread_log', 0) > 30:
                        self.log_message(f"📊 LLM Thread Usage: {self.llm_thread_pool.activeThreadCount()}/{self.llm_thread_pool.maxThreadCount()} threads")
                        self.performance_metrics['last_llm_thread_log'] = current_time

            # Log performance summary every 10 minutes (reduced frequency)
            if current_time - self.performance_metrics.get('last_performance_log', 0) > 600:
                # Only log if there are performance issues
                if cpu_percent > 50 or memory_mb > 500:
                    print(f"📊 Performance: CPU {cpu_percent:.1f}%, RAM {memory_mb:.0f}MB, Threads {thread_count}")
                self.performance_metrics['last_performance_log'] = current_time

        except Exception as e:
            print(f"Error monitoring performance: {e}")

    def optimize_for_performance(self):
        """Apply performance optimizations based on current system load"""
        try:
            # Get current system load
            cpu_percent = self.performance_metrics.get('cpu_percent', 0)
            memory_mb = self.performance_metrics.get('memory_usage_mb', 0)

            # Adaptive optimization based on load
            if cpu_percent > 70 or memory_mb > 800:
                # High load - reduce update frequencies
                if hasattr(self, 'master_timer'):
                    self.update_schedule.update({
                        'bid_ask': 5,           # Reduce from 3 to 5 seconds
                        'positions_orders': 10, # Reduce from 5 to 10 seconds
                        'chart': 30,            # Reduce from 15 to 30 seconds
                    })
                # Only log first time
                if not hasattr(self, '_high_load_logged'):
                    print("🐌 High system load - reducing update frequencies")
                    self._high_load_logged = True

            elif cpu_percent < 30 and memory_mb < 400:
                # Low load - can increase update frequencies
                if hasattr(self, 'master_timer'):
                    self.update_schedule.update({
                        'bid_ask': 2,           # Increase from 3 to 2 seconds
                        'positions_orders': 3,  # Increase from 5 to 3 seconds
                        'chart': 10,            # Increase from 15 to 10 seconds
                    })
                # Only log first time
                if not hasattr(self, '_low_load_logged'):
                    print("🚀 Low system load - increasing update frequencies")
                    self._low_load_logged = True

        except Exception as e:
            print(f"Error optimizing for performance: {e}")

    def setup_symbol_scanner(self):
        """Setup the dynamic symbol scanner"""
        try:
            if not SymbolScanner or not exchange:
                self.log_message("⚠️ Symbol scanner not available (missing dependencies)")
                if hasattr(self, 'dynamic_scan_cb'):
                    self.dynamic_scan_cb.setEnabled(False)
                    self.dynamic_scan_cb.setToolTip("Symbol scanner not available")
                return

            # Get available symbols from exchange
            try:
                markets = exchange.load_markets()
                usdt_symbols = [symbol for symbol in markets.keys()
                              if symbol.endswith('/USDT:USDT') and markets[symbol]['active']]

                # Use top 8 most liquid symbols if available
                if len(usdt_symbols) > 8:
                    # Sort by volume or use predefined list
                    usdt_symbols = usdt_symbols[:8]

                if not usdt_symbols:
                    usdt_symbols = SymbolScannerConfig.DEFAULT_SYMBOLS

            except Exception as e:
                self.log_message(f"⚠️ Could not fetch symbols from exchange: {e}")
                usdt_symbols = SymbolScannerConfig.DEFAULT_SYMBOLS

            # Initialize symbol scanner
            self.symbol_scanner = SymbolScannerConfig.create_scanner(
                market_api=exchange,
                symbols=usdt_symbols
            )

            # Initialize scanner state
            self.scanner_enabled = False
            self.current_symbol = self.symbol_combo.currentText()
            self.last_manual_change = 0  # Timestamp of last manual symbol change

            # Scalping mode flag - disables verbose analysis for speed
            self.scalping_mode = True  # Enable aggressive scalping mode by default

            self.log_message(f"✅ Symbol scanner initialized with {len(usdt_symbols)} symbols")

        except Exception as e:
            self.log_message(f"❌ Error setting up symbol scanner: {e}")
            self.symbol_scanner = None

    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_analysis(self):
        """Start comprehensive LLM-driven trading analysis"""
        symbol = self.symbol_combo.currentText()
        use_live = self.live_data_checkbox.isChecked()

        self.is_analyzing = True
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Enable performance mode to reduce lag during analysis
        self.set_performance_mode(True)

        self.batch_gui_update('system_status_label', 'both', "🧠 LLM ORCHESTRATOR ACTIVE",
                             f"color: {MatrixTheme.CYAN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message(f"🚀 Starting LLM Orchestrator analysis for {symbol} (Live: {use_live})")

        # Use new LLM orchestrator if available, otherwise fallback to legacy analysis
        if hasattr(self, 'llm_orchestrator') and self.llm_orchestrator:
            QTimer.singleShot(1000, self.run_llm_orchestrator_analysis)  # Fast LLM analysis
        else:
            self.log_message("⚠️ LLM Orchestrator not available - using legacy analysis")
            QTimer.singleShot(3000, self.complete_analysis)  # Fallback to legacy

    def run_llm_orchestrator_analysis(self):
        """Run comprehensive LLM orchestrator analysis cycle"""
        try:
            from core.llm_orchestrator import TradingContext

            # Build trading context
            symbol = self.symbol_combo.currentText()
            current_price = getattr(self, 'current_bid', 0.17) or getattr(self, 'current_ask', 0.17) or 0.17

            # Get account balance
            account_balance = 50.0  # Default
            try:
                if hasattr(self, 'real_trading') and self.real_trading:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info and 'USDT' in balance_info:
                        account_balance = balance_info['USDT'].get('free', 50.0)
            except:
                pass

            # Get open positions using direct CCXT
            open_positions = []
            try:
                # Use direct CCXT for most up-to-date data
                positions = fetch_open_positions()
                for pos in positions:
                    # 🚀 FIXED: Use current_time to avoid variable shadowing
                    current_time = time.time()
                    pos['time_held'] = current_time - pos.get('entry_time', current_time)
                    open_positions.append(pos)
            except Exception as e:
                self.log_message(f"⚠️ Error fetching positions for LLM: {e}")
                pass

            # Build market data
            market_data = {
                'bid': getattr(self, 'current_bid', current_price * 0.9995),
                'ask': getattr(self, 'current_ask', current_price * 1.0005),
                'spread_pct': 0.1,  # Default spread
                'volume_ratio': 1.0,
                'volatility': 2.0,
                'momentum': 0.0,
                'volume_trend': 'NORMAL',
                'support_level': current_price * 0.995,
                'resistance_level': current_price * 1.005,
                'signals': {
                    'ensemble_decision': 'NEUTRAL',
                    'confidence': 60.0,
                    'technical_signal': 'NEUTRAL',
                    'momentum_score': 0.0,
                    'volume_confirmation': 'PENDING',
                    'orderflow_bias': 'NEUTRAL'
                }
            }

            # Build performance metrics
            performance_metrics = {
                'trades_24h': 0,
                'win_rate_24h': 50.0,
                'avg_profit': 0.8,
                'avg_loss': -0.3,
                'sharpe_ratio': 1.0,
                'max_dd': 0.0,
                'total_pnl_24h': 0.0,
                'roi_24h': 0.0,
                'current_risk_pct': 2.0,
                'avg_hold_time': 8.0,
                'entry_threshold': 70.0,
                'exit_threshold': 60.0,
                'sizing_method': 'FIXED_RISK',
                'max_positions': 3,
                'daily_pnl': 0.0,
                'daily_win_rate': 50.0,
                'max_drawdown': 0.0
            }

            # Create trading context
            trading_context = TradingContext(
                symbol=symbol,
                current_price=current_price,
                account_balance=account_balance,
                open_positions=open_positions,
                market_data=market_data,
                performance_metrics=performance_metrics,
                emergency_flags=[],
                timestamp=datetime.now(),
                recent_prices=[(datetime.now(), current_price)],  # Add recent price data
                recent_signals=[{'decision': 'WAIT', 'timestamp': datetime.now()}]  # Add recent signals
            )

            self.log_message(f"🎯 LLM Orchestrator Context: {symbol} @ ${current_price:.6f}, Balance: ${account_balance:.2f}, Positions: {len(open_positions)}")

            # 🚀 PERFORMANCE: Execute LLM orchestrator cycle in scalping mode for speed
            execution_mode = "scalping" if hasattr(self, 'scalping_mode') and self.scalping_mode else "full"
            cycle_results = self.llm_orchestrator.execute_prompt_cycle(trading_context, mode=execution_mode)

            # Process and display results
            self.process_llm_orchestrator_results(cycle_results, trading_context)

            # Complete analysis
            self.complete_llm_orchestrator_analysis(cycle_results)

        except Exception as e:
            self.log_message(f"❌ Error in LLM orchestrator analysis: {e}")
            # Fallback to legacy analysis
            self.complete_analysis()

    def process_llm_orchestrator_results(self, cycle_results, context):
        """Process and display LLM orchestrator results"""
        try:
            results_summary = []

            for prompt_type, result in cycle_results.items():
                if result.success:
                    action = result.response.get('ACTION', 'UNKNOWN')
                    confidence = result.response.get('CONFIDENCE', 0)
                    results_summary.append(f"{prompt_type.value}: {action} ({confidence:.0f}%)")
                else:
                    results_summary.append(f"{prompt_type.value}: ERROR")

            if results_summary:
                self.log_message(f"🧠 LLM Results: {', '.join(results_summary)}")

            # Update GUI with key results
            if 'position_management' in [pt.value for pt in cycle_results.keys()]:
                pos_result = next((r for pt, r in cycle_results.items() if pt.value == 'position_management'), None)
                if pos_result and pos_result.success:
                    action = pos_result.response.get('ACTION', 'HOLD')
                    self.batch_gui_update('llm_decision_label', 'text', f"Position Mgmt: {action}")

            if 'market_regime' in [pt.value for pt in cycle_results.keys()]:
                regime_result = next((r for pt, r in cycle_results.items() if pt.value == 'market_regime'), None)
                if regime_result and regime_result.success:
                    regime = regime_result.response.get('REGIME', 'UNKNOWN')
                    suitability = regime_result.response.get('SCALP_SUITABILITY', 'MEDIUM')
                    self.batch_gui_update('llm_reasoning_text', 'text', f"Market Regime: {regime} (Scalp: {suitability})")

        except Exception as e:
            self.log_message(f"❌ Error processing LLM orchestrator results: {e}")

    def complete_llm_orchestrator_analysis(self, cycle_results):
        """Complete LLM orchestrator analysis and update final verdict with proper aggregation"""
        try:
            # 🚀 FIXED: Comprehensive LLM decision aggregation
            decision_votes = {"LONG": 0, "SHORT": 0, "WAIT": 0, "CLOSE": 0}
            confidence_sum = 0
            confidence_count = 0
            reasoning_parts = []

            # Extract decisions from all successful prompt results
            prompt_decisions = {}

            # Market Regime Analysis
            regime_result = next((r for pt, r in cycle_results.items() if pt.value == 'market_regime'), None)
            if regime_result and regime_result.success:
                regime = regime_result.response.get('REGIME', 'UNKNOWN')
                regime_conf = regime_result.response.get('CONFIDENCE', 50)
                scalp_suit = regime_result.response.get('SCALP_SUITABILITY', 'MEDIUM')

                # Convert regime to trading bias
                if regime in ['TRENDING_BULL', 'BREAKOUT_PENDING'] and scalp_suit in ['HIGH', 'MEDIUM']:
                    decision_votes["LONG"] += 1.5  # Strong bullish bias
                elif regime in ['TRENDING_BEAR'] and scalp_suit in ['HIGH', 'MEDIUM']:
                    decision_votes["SHORT"] += 1.5  # Strong bearish bias
                elif regime in ['RANGING_TIGHT', 'RANGING_VOLATILE'] and scalp_suit == 'HIGH':
                    decision_votes["LONG"] += 0.5  # Slight bias for range trading
                    decision_votes["SHORT"] += 0.5
                else:
                    decision_votes["WAIT"] += 1.0

                confidence_sum += regime_conf
                confidence_count += 1
                reasoning_parts.append(f"Market: {regime} ({scalp_suit} scalp)")
                prompt_decisions['market_regime'] = f"{regime} {regime_conf}%"

            # Risk Assessment
            risk_result = next((r for pt, r in cycle_results.items() if pt.value == 'risk_assessment'), None)
            if risk_result and risk_result.success:
                approved = risk_result.response.get('APPROVED', False)
                risk_score = risk_result.response.get('RISK_SCORE', 100)
                risk_conf = risk_result.response.get('CONFIDENCE', 50)
                risk_decision = risk_result.response.get('DECISION', None)  # 🚀 FIXED: Extract actual decision

                # 🚀 FIXED: Use actual trading decision if available
                if risk_decision in ['LONG', 'SHORT']:
                    if approved and risk_score < 70:
                        # Strong signal for specific direction
                        decision_votes[risk_decision] += 2.0
                        reasoning_parts.append(f"Risk: {risk_decision} APPROVED (Score: {risk_score})")
                    elif approved:
                        # Medium signal for specific direction
                        decision_votes[risk_decision] += 1.0
                        reasoning_parts.append(f"Risk: {risk_decision} APPROVED (Score: {risk_score})")
                    else:
                        # Risk rejected but still has directional bias
                        decision_votes["WAIT"] += 1.5
                        reasoning_parts.append(f"Risk: {risk_decision} REJECTED (Score: {risk_score})")
                else:
                    # Fallback to old logic if no decision available
                    if approved and risk_score < 70:
                        decision_votes["LONG"] += 1.0
                        decision_votes["SHORT"] += 1.0
                        reasoning_parts.append(f"Risk: APPROVED (Score: {risk_score})")
                    elif approved:
                        decision_votes["LONG"] += 0.5
                        decision_votes["SHORT"] += 0.5
                        reasoning_parts.append(f"Risk: APPROVED (Score: {risk_score})")
                    else:
                        decision_votes["WAIT"] += 2.0
                        reasoning_parts.append(f"Risk: REJECTED (Score: {risk_score})")

                confidence_sum += risk_conf
                confidence_count += 1
                # 🚀 FIXED: Correct display logic to show decision + approval status
                if risk_decision in ['LONG', 'SHORT']:
                    decision_display = f"{risk_decision} {'APPROVED' if approved else 'REJECTED'}"
                else:
                    decision_display = 'APPROVED' if approved else 'REJECTED'
                prompt_decisions['risk_assessment'] = f"{decision_display} {risk_conf}%"

            # Entry Timing
            entry_result = next((r for pt, r in cycle_results.items() if pt.value == 'entry_timing'), None)
            if entry_result and entry_result.success:
                entry_action = entry_result.response.get('ACTION', 'WAIT')
                entry_conf = entry_result.response.get('CONFIDENCE', 50)
                entry_decision = entry_result.response.get('DECISION', None)  # 🚀 FIXED: Extract actual decision

                # 🚀 FIXED: Use actual trading decision for directional voting
                if entry_action == 'ENTER_NOW' and entry_decision in ['LONG', 'SHORT']:
                    # Strong signal for specific direction
                    decision_votes[entry_decision] += 3.0  # Increased weight for entry timing
                    reasoning_parts.append(f"Entry: {entry_decision} NOW")
                elif entry_action == 'ENTER_NOW':
                    # Fallback: if no specific direction, add to both (old behavior)
                    decision_votes["LONG"] += 1.5
                    decision_votes["SHORT"] += 1.5
                    reasoning_parts.append("Entry: NOW")
                elif entry_action == 'WAIT':
                    decision_votes["WAIT"] += 1.5
                    reasoning_parts.append("Entry: WAIT")
                else:  # ABORT
                    decision_votes["WAIT"] += 2.0
                    reasoning_parts.append("Entry: ABORT")

                confidence_sum += entry_conf
                confidence_count += 1
                decision_display = f"{entry_decision} {entry_action}" if entry_decision else entry_action
                prompt_decisions['entry_timing'] = f"{decision_display} {entry_conf}%"

            # Strategy Adaptation
            strategy_result = next((r for pt, r in cycle_results.items() if pt.value == 'strategy_adaptation'), None)
            if strategy_result and strategy_result.success:
                risk_adj = strategy_result.response.get('RISK_ADJUSTMENT', 1.0)
                strategy_conf = strategy_result.response.get('CONFIDENCE', 50)

                if risk_adj > 1.2:
                    # Aggressive strategy
                    decision_votes["LONG"] += 0.8
                    decision_votes["SHORT"] += 0.8
                    reasoning_parts.append(f"Strategy: AGGRESSIVE ({risk_adj:.1f}x)")
                elif risk_adj < 0.8:
                    # Conservative strategy
                    decision_votes["WAIT"] += 1.0
                    reasoning_parts.append(f"Strategy: CONSERVATIVE ({risk_adj:.1f}x)")
                else:
                    reasoning_parts.append(f"Strategy: NORMAL ({risk_adj:.1f}x)")

                confidence_sum += strategy_conf
                confidence_count += 1
                prompt_decisions['strategy_adaptation'] = f"{risk_adj:.1f}x {strategy_conf}%"

            # Opportunity Scanner
            opp_result = next((r for pt, r in cycle_results.items() if pt.value == 'opportunity_scanner'), None)
            if opp_result and opp_result.success:
                best_opp = opp_result.response.get('BEST_OPPORTUNITY', 'NONE')
                setup_type = opp_result.response.get('SETUP_TYPE', 'NONE')
                opp_conf = opp_result.response.get('CONFIDENCE', 50)
                opp_decision = opp_result.response.get('DECISION', None)  # 🚀 FIXED: Extract actual decision

                # 🚀 FIXED: Use actual trading decision if available
                if opp_decision in ['LONG', 'SHORT']:
                    if best_opp != 'NONE' and setup_type in ['BREAKOUT', 'MOMENTUM', 'TREND_CONTINUATION']:
                        # Strong signal for specific direction
                        decision_votes[opp_decision] += 2.0
                        reasoning_parts.append(f"Opportunity: {opp_decision} {setup_type}")
                    elif setup_type == 'REVERSAL':
                        # Reversal signal for specific direction
                        decision_votes[opp_decision] += 1.5
                        reasoning_parts.append(f"Opportunity: {opp_decision} {setup_type}")
                    else:
                        # Weak signal but still directional
                        decision_votes[opp_decision] += 0.8
                        reasoning_parts.append(f"Opportunity: {opp_decision} WEAK")
                else:
                    # Fallback to old logic if no decision available
                    if best_opp != 'NONE' and setup_type in ['BREAKOUT', 'MOMENTUM', 'TREND_CONTINUATION']:
                        decision_votes["LONG"] += 1.2
                        decision_votes["SHORT"] += 1.2
                        reasoning_parts.append(f"Opportunity: {setup_type}")
                    elif setup_type == 'REVERSAL':
                        decision_votes["SHORT"] += 1.0  # Reversal bias
                        reasoning_parts.append(f"Opportunity: {setup_type}")
                    else:
                        decision_votes["WAIT"] += 0.5
                        reasoning_parts.append("Opportunity: NONE")

                confidence_sum += opp_conf
                confidence_count += 1
                decision_display = f"{opp_decision} {best_opp}" if opp_decision else best_opp
                prompt_decisions['opportunity_scanner'] = f"{decision_display} {opp_conf}%"

            # Position Management (for existing positions)
            pos_result = next((r for pt, r in cycle_results.items() if pt.value == 'position_management'), None)
            if pos_result and pos_result.success:
                pos_action = pos_result.response.get('ACTION', 'HOLD')
                pos_conf = pos_result.response.get('CONFIDENCE', 50)

                if pos_action in ['CLOSE', 'PARTIAL_CLOSE']:
                    decision_votes["CLOSE"] += 3.0  # Strong signal to close
                    reasoning_parts.append(f"Position: {pos_action}")
                else:
                    reasoning_parts.append("Position: HOLD")

                confidence_sum += pos_conf
                confidence_count += 1
                prompt_decisions['position_management'] = f"{pos_action} {pos_conf}%"

            # 🚀 CRITICAL FIX: Implement conflict resolution and emergency stops
            emergency_stop_reason = self.check_emergency_conditions(cycle_results, decision_votes)

            if emergency_stop_reason:
                decision = "WAIT"
                confidence = 50.0
                reasoning_parts.append(f"EMERGENCY STOP: {emergency_stop_reason}")
                self.log_message(f"🚨 EMERGENCY STOP: {emergency_stop_reason}")
            else:
                # Apply weighted decision logic with conflict resolution
                decision = self.resolve_signal_conflicts(cycle_results, decision_votes)

                # 🚀 AGGRESSIVE SCALPING BIAS: Prefer action over waiting
                if decision == "CLOSE" and decision_votes["CLOSE"] > 1.0:
                    pass  # Keep CLOSE decision
                elif decision_votes["LONG"] > decision_votes["SHORT"]:
                    if decision_votes["LONG"] > decision_votes["WAIT"] * 0.3:
                        decision = "LONG"
                    elif decision_votes["LONG"] > 0.5:
                        decision = "LONG"
                    else:
                        decision = "LONG" if decision_votes["LONG"] >= decision_votes["SHORT"] else "SHORT"
                elif decision_votes["SHORT"] > decision_votes["LONG"]:
                    if decision_votes["SHORT"] > decision_votes["WAIT"] * 0.3:
                        decision = "SHORT"
                    elif decision_votes["SHORT"] > 0.5:
                        decision = "SHORT"
                    else:
                        decision = "SHORT" if decision_votes["SHORT"] >= decision_votes["LONG"] else "LONG"
                else:
                    # Tie-breaker logic
                    if decision_votes["LONG"] + decision_votes["SHORT"] > decision_votes["WAIT"]:
                        decision = "LONG" if decision_votes["LONG"] >= decision_votes["SHORT"] else "SHORT"
                    elif decision_votes["LONG"] > 0 or decision_votes["SHORT"] > 0:
                        decision = "LONG" if decision_votes["LONG"] >= decision_votes["SHORT"] else "SHORT"
                    else:
                        decision = "WAIT"

            # 🚀 FIXED: Calculate weighted confidence with improved aggregation
            if confidence_count > 0:
                # Weighted confidence calculation based on prompt importance
                confidence = self.calculate_weighted_confidence(cycle_results, decision_votes, emergency_stop_reason)
            else:
                confidence = 50.0

            # 🚀 FIXED: Build comprehensive reasoning
            reasoning = f"LLM Orchestrator: {', '.join(reasoning_parts[:3])}"  # Limit length

            # Log detailed analysis
            self.log_message(f"🧠 LLM Prompt Results: {prompt_decisions}")
            self.log_message(f"🎯 Decision Votes: LONG={decision_votes['LONG']:.1f}, SHORT={decision_votes['SHORT']:.1f}, WAIT={decision_votes['WAIT']:.1f}, CLOSE={decision_votes['CLOSE']:.1f}")

            # Store for other systems
            self.last_llm_decision = decision
            self.last_llm_confidence = confidence

            # Update final verdict display
            self.batch_gui_update('decision_label', 'text', f"Decision: {decision}")
            self.batch_gui_update('confidence_label', 'text', f"Confidence: {confidence:.1f}%")
            self.batch_gui_update('last_update_label', 'text', f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

            # Color code decision
            if decision == "LONG":
                color = MatrixTheme.GREEN
            elif decision == "SHORT":
                color = MatrixTheme.RED
            elif decision == "CLOSE":
                color = MatrixTheme.CYAN
            else:
                color = MatrixTheme.YELLOW

            decision_style = f"""
                font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
                font-weight: bold;
                color: {color};
                padding: 5px;
            """
            self.batch_gui_update('decision_label', 'style', decision_style)

            # Log final decision
            self.log_message(f"🎯 LLM Orchestrator Final Decision: {decision} ({confidence:.1f}%) - {reasoning}")

            # 🚀 CRITICAL: Execute trades for high-confidence decisions
            if decision in ['LONG', 'SHORT'] and confidence > 85 and not emergency_stop_reason:
                self.execute_llm_decision(decision, confidence, cycle_results)

            # Update system status
            self.batch_gui_update('system_status_label', 'both', "🧠 LLM ANALYSIS COMPLETE",
                                 f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

            # 🚀 FIXED: Check auto-refresh BEFORE disabling analysis mode
            auto_refresh_enabled = self.auto_refresh_checkbox.isChecked()

            # Re-enable controls
            self.is_analyzing = False
            self.analyze_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.set_performance_mode(False)

            # 🚀 FIXED: Auto-refresh if enabled (check before is_analyzing was set to False)
            if auto_refresh_enabled:
                self.analysis_timer = QTimer()
                self.analysis_timer.setSingleShot(True)
                self.analysis_timer.timeout.connect(self.start_analysis)
                self.analysis_timer.start(60000)  # 60 seconds for auto-refresh
                self.log_message("🔄 Auto-refresh scheduled in 60 seconds")

        except Exception as e:
            self.log_message(f"❌ Error completing LLM orchestrator analysis: {e}")
            # Fallback to legacy completion
            self.complete_analysis()

    def check_emergency_conditions(self, cycle_results, decision_votes):
        """🚨 Check for emergency stop conditions in LLM decisions"""
        try:
            # Check for conflicting high-confidence signals
            high_confidence_signals = {}

            for prompt_type, result in cycle_results.items():
                if result.success and result.confidence > 80:
                    decision = result.response.get('DECISION') or result.response.get('ACTION')
                    if decision in ['LONG', 'SHORT']:
                        if decision not in high_confidence_signals:
                            high_confidence_signals[decision] = []
                        high_confidence_signals[decision].append({
                            'prompt': prompt_type.value,
                            'confidence': result.confidence
                        })

            # Emergency stop if conflicting high-confidence signals
            if len(high_confidence_signals) > 1:
                long_signals = high_confidence_signals.get('LONG', [])
                short_signals = high_confidence_signals.get('SHORT', [])

                if long_signals and short_signals:
                    max_long_conf = max([s['confidence'] for s in long_signals])
                    max_short_conf = max([s['confidence'] for s in short_signals])

                    if max_long_conf > 80 and max_short_conf > 80:
                        return f"CONFLICTING_SIGNALS: LONG({max_long_conf:.0f}%) vs SHORT({max_short_conf:.0f}%)"

            # Check for market regime uncertainty
            regime_result = next((r for pt, r in cycle_results.items() if pt.value == 'market_regime'), None)
            if regime_result and regime_result.success:
                regime_conf = regime_result.response.get('CONFIDENCE', 100)
                if regime_conf < 60:
                    return f"UNCERTAIN_MARKET: Regime confidence {regime_conf:.0f}%"

            # Check for excessive WAIT votes (indicates system uncertainty)
            total_votes = sum(decision_votes.values())
            wait_percentage = (decision_votes.get('WAIT', 0) / total_votes * 100) if total_votes > 0 else 0
            if wait_percentage > 60:
                return f"HIGH_UNCERTAINTY: {wait_percentage:.0f}% WAIT votes"

            return None

        except Exception as e:
            self.log_message(f"❌ Error checking emergency conditions: {e}")
            return "ERROR_IN_EMERGENCY_CHECK"

    def resolve_signal_conflicts(self, cycle_results, decision_votes):
        """🎯 Resolve conflicts using weighted hierarchy: Risk(0.3) > Entry(0.25) > Opportunity(0.2)"""
        try:
            # Prompt hierarchy weights
            prompt_weights = {
                'risk_assessment': 0.30,
                'entry_timing': 0.25,
                'opportunity_scanner': 0.20,
                'market_regime': 0.15,
                'strategy_adaptation': 0.10
            }

            # Calculate weighted decision scores
            weighted_scores = {'LONG': 0, 'SHORT': 0, 'WAIT': 0, 'CLOSE': 0}

            for prompt_type, result in cycle_results.items():
                if result.success:
                    weight = prompt_weights.get(prompt_type.value, 0.05)
                    confidence_factor = result.confidence / 100.0

                    decision = result.response.get('DECISION') or result.response.get('ACTION', 'WAIT')
                    if decision in weighted_scores:
                        weighted_scores[decision] += weight * confidence_factor

            # Return highest weighted decision
            if weighted_scores:
                return max(weighted_scores, key=weighted_scores.get)
            else:
                # Fallback to vote-based decision
                return max(decision_votes, key=decision_votes.get) if decision_votes else 'WAIT'

        except Exception as e:
            self.log_message(f"❌ Error resolving signal conflicts: {e}")
            return 'WAIT'

    def calculate_weighted_confidence(self, cycle_results, decision_votes, emergency_stop_reason):
        """📊 Calculate confidence using weighted average and consensus strength"""
        try:
            if emergency_stop_reason:
                return 50.0  # Low confidence for emergency stops

            # Prompt importance weights
            prompt_weights = {
                'risk_assessment': 0.30,
                'entry_timing': 0.25,
                'opportunity_scanner': 0.20,
                'market_regime': 0.15,
                'strategy_adaptation': 0.10
            }

            weighted_confidence_sum = 0
            total_weight = 0

            for prompt_type, result in cycle_results.items():
                if result.success:
                    weight = prompt_weights.get(prompt_type.value, 0.05)
                    weighted_confidence_sum += result.confidence * weight
                    total_weight += weight

            if total_weight > 0:
                base_confidence = weighted_confidence_sum / total_weight
            else:
                base_confidence = 50.0

            # Apply consensus boost (max 10% instead of 20%)
            max_votes = max(decision_votes.values()) if decision_votes else 0
            total_votes = sum(decision_votes.values()) if decision_votes else 1
            consensus_strength = max_votes / total_votes if total_votes > 0 else 0

            # Reduced consensus boost for more conservative confidence
            final_confidence = min(95, base_confidence + (consensus_strength * 10))

            return final_confidence

        except Exception as e:
            self.log_message(f"❌ Error calculating weighted confidence: {e}")
            return 50.0

    def execute_llm_decision(self, decision, confidence, cycle_results):
        """💰 Execute trades based on LLM orchestrator decisions"""
        try:
            # Get current trading parameters
            symbol = self.symbol_combo.currentText()
            current_price = getattr(self, 'current_bid', 0.17) or getattr(self, 'current_ask', 0.17) or 0.17

            # Get account balance
            account_balance = 50.0  # Default
            try:
                if hasattr(self, 'real_trading') and self.real_trading:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info and 'USDT' in balance_info:
                        account_balance = balance_info['USDT'].get('free', 50.0)
            except:
                pass

            # Calculate position size
            position_size = self.calculate_dynamic_position_size(
                account_balance, current_price, confidence, decision
            )

            if position_size <= 0:
                self.log_message(f"⚠️ Position size too small: {position_size}")
                return False

            # Validate trade parameters
            if not self.validate_trade_execution(symbol, position_size, account_balance):
                return False

            # Execute the trade using the correct RealTradingInterface methods
            self.log_message(f"🚀 EXECUTING LLM DECISION: {decision} {position_size:.4f} {symbol} @ {current_price:.6f}")
            self.log_message(f"📊 Decision Confidence: {confidence:.1f}% | Balance: ${account_balance:.2f}")

            # Place market order for immediate execution using correct interface methods
            if decision == "LONG":
                success = self.real_trading.place_market_long(symbol, position_size, 20)  # 20x leverage
            else:  # SHORT
                success = self.real_trading.place_market_short(symbol, position_size, 20)  # 20x leverage

            if success:
                self.log_message(f"✅ LLM Trade Executed: {decision} {position_size:.4f} {symbol}")

                # Update trading statistics
                self.update_llm_trading_stats(decision, confidence, position_size, current_price)

                return True
            else:
                self.log_message(f"❌ LLM Trade Failed: {decision} {position_size:.4f} {symbol}")
                return False

        except Exception as e:
            self.log_message(f"❌ Error executing LLM decision: {e}")
            return False

    def calculate_dynamic_position_size(self, balance, price, confidence, decision):
        """📏 ENHANCED: Calculate position size with intelligent account preservation logic"""
        try:
            # 🚨 ACCOUNT HEALTH ASSESSMENT
            account_health = self.assess_account_health(balance)

            # 🚨 CRITICAL: Emergency stop if account is in danger
            if account_health['status'] == 'EMERGENCY_STOP':
                self.log_message(f"🚨 EMERGENCY STOP: Account balance ${balance:.2f} below critical threshold")
                self.log_message(f"🛑 ALL TRADING SUSPENDED - Account preservation mode activated")
                return 0

            # 🚨 CAPITAL PRESERVATION MODE
            if account_health['status'] == 'CAPITAL_PRESERVATION':
                self.log_message(f"🛡️ CAPITAL PRESERVATION MODE: Balance ${balance:.2f} critically low")
                self.log_message(f"🚫 NO NEW TRADES - Protecting remaining capital")
                return 0

            # Get existing positions for portfolio risk assessment
            existing_positions = self.get_current_positions_value()
            total_exposure = existing_positions['total_notional']
            position_count = existing_positions['count']

            # 🚨 PORTFOLIO RISK CHECK: Prevent over-exposure
            portfolio_risk_pct = (total_exposure / balance) * 100 if balance > 0 else 0
            if portfolio_risk_pct > 80:
                self.log_message(f"🚨 PORTFOLIO RISK LIMIT: Current exposure {portfolio_risk_pct:.1f}% > 80%")
                self.log_message(f"🛑 Trade rejected - Portfolio over-exposed")
                return 0

            # Apply account health-based risk scaling
            base_risk_percentage = 2.0  # Your configured 2% risk
            adjusted_risk_percentage = base_risk_percentage * account_health['risk_multiplier']

            # Apply position count penalty (more positions = smaller new positions)
            position_penalty = max(0.3, 1.0 - (position_count * 0.2))  # Reduce by 20% per existing position
            adjusted_risk_percentage *= position_penalty

            leverage = 20  # 20x leverage
            margin_buffer = account_health['margin_buffer']  # Dynamic based on account health

            # Confidence-based adjustment (more conservative when account is stressed)
            confidence_multiplier = max(0.3, min(0.8, confidence / 100.0))  # Reduced max from 0.9 to 0.8

            # Account health-based balance utilization
            max_usable_balance = balance * account_health['max_balance_usage']

            # Get real-time HTX balance to double-check
            actual_available_margin = self.get_htx_available_margin()
            if actual_available_margin is not None and actual_available_margin < max_usable_balance:
                max_usable_balance = actual_available_margin * 0.9
                self.log_message(f"📊 Using HTX reported available margin: ${actual_available_margin:.2f}")

            # Calculate ultra-conservative position size
            max_margin_for_position = max_usable_balance * confidence_multiplier

            # Apply intelligent risk scaling
            risk_amount = balance * (adjusted_risk_percentage / 100.0) * confidence_multiplier

            # Use the smaller of margin-limited or risk-limited
            available_margin_for_trade = min(max_margin_for_position, risk_amount)

            # Calculate position size with enhanced safety
            max_notional_with_margin = available_margin_for_trade * leverage / margin_buffer
            position_size = max_notional_with_margin / price

            # Account health-based minimum position size
            min_position_size = account_health['min_position_size']
            if position_size < min_position_size:
                self.log_message(f"⚠️ Position size {position_size:.4f} below minimum {min_position_size}")
                return 0

            # Calculate final margin requirement
            notional_value = position_size * price
            required_margin = (notional_value / leverage) * margin_buffer

            # Enhanced safety checks
            if required_margin > max_usable_balance:
                safe_notional = max_usable_balance * leverage / margin_buffer
                position_size = safe_notional / price
                notional_value = position_size * price
                required_margin = (notional_value / leverage) * margin_buffer
                self.log_message(f"⚠️ Position size reduced for safety: {position_size:.4f} units")

            # Final liquidation risk check
            new_total_exposure = total_exposure + notional_value
            liquidation_risk_pct = (new_total_exposure / balance) * 100 if balance > 0 else 100

            if liquidation_risk_pct > account_health['max_exposure_pct']:
                self.log_message(f"🚨 LIQUIDATION RISK: New exposure would be {liquidation_risk_pct:.1f}%")
                self.log_message(f"🛑 Trade rejected - Exceeds {account_health['max_exposure_pct']:.1f}% exposure limit")
                return 0

            # Enhanced logging with account health context
            self.log_message(f"📏 INTELLIGENT POSITION SIZING ({account_health['status']}):")
            self.log_message(f"   💰 Balance: ${balance:.2f} | Health: {account_health['status']}")
            self.log_message(f"   📊 Risk: {adjusted_risk_percentage:.2f}% (base: {base_risk_percentage:.1f}%) | Confidence: {confidence:.1f}%")
            self.log_message(f"   🎯 Position Size: {position_size:.4f} units | Notional: ${notional_value:.2f}")
            self.log_message(f"   🛡️ Required Margin: ${required_margin:.2f} | Buffer: {margin_buffer:.1f}x")
            self.log_message(f"   📈 Portfolio Exposure: {portfolio_risk_pct:.1f}% → {liquidation_risk_pct:.1f}%")
            self.log_message(f"   🔒 Existing Positions: {position_count} | Total Value: ${total_exposure:.2f}")

            return position_size

        except Exception as e:
            self.log_message(f"❌ Error calculating position size: {e}")
            return 0

    def assess_account_health(self, balance):
        """🚨 CRITICAL: Assess account health and determine risk parameters"""
        try:
            # Account health thresholds
            emergency_threshold = 5.0      # Below $5 = Emergency stop
            critical_threshold = 10.0      # Below $10 = Capital preservation
            warning_threshold = 20.0       # Below $20 = Reduced risk
            healthy_threshold = 50.0       # Above $50 = Normal operation

            if balance < emergency_threshold:
                return {
                    'status': 'EMERGENCY_STOP',
                    'risk_multiplier': 0.0,        # No trading
                    'margin_buffer': 5.0,          # Maximum safety
                    'max_balance_usage': 0.0,      # No balance usage
                    'max_exposure_pct': 0.0,       # No exposure allowed
                    'min_position_size': 999999    # Impossible to meet
                }
            elif balance < critical_threshold:
                return {
                    'status': 'CAPITAL_PRESERVATION',
                    'risk_multiplier': 0.0,        # No new trades
                    'margin_buffer': 4.0,          # High safety
                    'max_balance_usage': 0.0,      # No balance usage
                    'max_exposure_pct': 0.0,       # No new exposure
                    'min_position_size': 999999    # Impossible to meet
                }
            elif balance < warning_threshold:
                return {
                    'status': 'HIGH_RISK',
                    'risk_multiplier': 0.25,       # 25% of normal risk (0.5% instead of 2%)
                    'margin_buffer': 3.0,          # High safety buffer
                    'max_balance_usage': 0.15,     # Use max 15% of balance
                    'max_exposure_pct': 30.0,      # Max 30% total exposure
                    'min_position_size': 1.0       # Minimum viable position
                }
            elif balance < healthy_threshold:
                return {
                    'status': 'MODERATE_RISK',
                    'risk_multiplier': 0.5,        # 50% of normal risk (1% instead of 2%)
                    'margin_buffer': 2.5,          # Moderate safety buffer
                    'max_balance_usage': 0.25,     # Use max 25% of balance
                    'max_exposure_pct': 50.0,      # Max 50% total exposure
                    'min_position_size': 1.0       # Minimum viable position
                }
            else:
                return {
                    'status': 'HEALTHY',
                    'risk_multiplier': 1.0,        # Full risk (2% as configured)
                    'margin_buffer': 2.0,          # Standard safety buffer
                    'max_balance_usage': 0.4,      # Use max 40% of balance
                    'max_exposure_pct': 70.0,      # Max 70% total exposure
                    'min_position_size': 1.0       # Minimum viable position
                }

        except Exception as e:
            self.log_message(f"❌ Error assessing account health: {e}")
            # Return ultra-conservative defaults on error
            return {
                'status': 'ERROR_CONSERVATIVE',
                'risk_multiplier': 0.1,
                'margin_buffer': 5.0,
                'max_balance_usage': 0.1,
                'max_exposure_pct': 20.0,
                'min_position_size': 1.0
            }

    def get_current_positions_value(self):
        """📊 Get current positions total value and count for portfolio risk assessment"""
        try:
            total_notional = 0.0
            position_count = 0
            positions_detail = []

            # Try to get positions from real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                try:
                    # Get positions using CCXT
                    positions = self.real_trading.exchange.fetch_positions()

                    for position in positions:
                        if position and position.get('contracts', 0) > 0:  # Open position
                            notional = abs(position.get('notional', 0) or 0)
                            if notional > 0:
                                total_notional += notional
                                position_count += 1
                                positions_detail.append({
                                    'symbol': position.get('symbol', 'UNKNOWN'),
                                    'side': position.get('side', 'UNKNOWN'),
                                    'size': position.get('contracts', 0),
                                    'notional': notional,
                                    'unrealized_pnl': position.get('unrealizedPnl', 0)
                                })

                except Exception as e:
                    self.log_message(f"⚠️ Could not fetch positions from exchange: {e}")

            # Log portfolio status
            if position_count > 0:
                self.log_message(f"📊 Portfolio Status: {position_count} positions, ${total_notional:.2f} total exposure")
                for pos in positions_detail:
                    pnl_str = f"PnL: ${pos['unrealized_pnl']:.2f}" if pos['unrealized_pnl'] != 0 else ""
                    self.log_message(f"   • {pos['symbol']}: {pos['side']} ${pos['notional']:.2f} {pnl_str}")
            else:
                self.log_message(f"📊 Portfolio Status: No open positions")

            return {
                'total_notional': total_notional,
                'count': position_count,
                'positions': positions_detail
            }

        except Exception as e:
            self.log_message(f"❌ Error getting current positions: {e}")
            return {
                'total_notional': 0.0,
                'count': 0,
                'positions': []
            }

    def get_htx_available_margin(self):
        """Get available margin from HTX exchange"""
        try:
            if hasattr(self, 'real_trading') and self.real_trading:
                balance_info = self.real_trading.get_balance_info()
                if balance_info and 'USDT' in balance_info:
                    # Try to get available margin from HTX response
                    usdt_info = balance_info['USDT']

                    # HTX might provide 'free' balance which is available for trading
                    available = usdt_info.get('free', 0)

                    # Some exchanges provide 'available' field specifically for margin
                    if 'available' in usdt_info:
                        available = usdt_info['available']

                    self.log_message(f"📊 HTX Available Margin: ${available:.2f}")
                    return available

            return None

        except Exception as e:
            self.log_message(f"⚠️ Could not get HTX available margin: {e}")
            return None

    def validate_trade_execution(self, symbol, position_size, balance):
        """✅ Validate trade parameters before execution with HTX margin checks"""
        try:
            # Check symbol format
            if not symbol or ':' not in symbol:
                self.log_message(f"❌ Invalid symbol format: {symbol}")
                return False

            # Check position size
            if position_size <= 0:
                self.log_message(f"❌ Invalid position size: {position_size}")
                return False

            # Check balance
            if balance < 10:  # Minimum $10 balance
                self.log_message(f"❌ Insufficient balance: ${balance:.2f}")
                return False

            # Check market data availability
            if not hasattr(self, 'current_bid') or not hasattr(self, 'current_ask'):
                self.log_message("❌ Market data not available")
                return False

            # Check trading interface
            if not hasattr(self, 'real_trading') or not self.real_trading:
                self.log_message("❌ Trading interface not available")
                return False

            # 🚀 HTX-SPECIFIC: Ultra-conservative margin validation
            current_price = getattr(self, 'current_bid', 0.17) or getattr(self, 'current_ask', 0.17) or 0.17
            leverage = 20
            margin_buffer = 2.0  # 100% safety buffer (same as calculation)

            # Calculate required margin with conservative buffer
            notional_value = position_size * current_price
            required_margin = (notional_value / leverage) * margin_buffer

            # Ultra-conservative check: never use more than 50% of balance
            max_allowed_margin = balance * 0.5

            if required_margin > max_allowed_margin:
                self.log_message(f"❌ HTX Ultra-Conservative Margin Check Failed:")
                self.log_message(f"   Required Margin: ${required_margin:.2f}")
                self.log_message(f"   Max Allowed (50% of balance): ${max_allowed_margin:.2f}")
                self.log_message(f"   Available Balance: ${balance:.2f}")
                self.log_message(f"   Shortfall: ${required_margin - max_allowed_margin:.2f}")
                return False

            # Check if position size is reasonable for the symbol
            if position_size > 10000:  # Maximum reasonable position size
                self.log_message(f"❌ Position size too large: {position_size:.2f} units")
                return False

            # Check if notional value is reasonable
            if notional_value < 1:  # Minimum $1 notional
                self.log_message(f"❌ Notional value too small: ${notional_value:.2f}")
                return False

            self.log_message(f"✅ Trade Validation Passed:")
            self.log_message(f"   Position: {position_size:.4f} units")
            self.log_message(f"   Notional: ${notional_value:.2f}")
            self.log_message(f"   Required Margin: ${required_margin:.2f}")
            self.log_message(f"   Available Balance: ${balance:.2f}")

            return True

        except Exception as e:
            self.log_message(f"❌ Error validating trade: {e}")
            return False



    def update_llm_trading_stats(self, decision, confidence, position_size, price):
        """📊 Update trading statistics for LLM decisions"""
        try:
            # Initialize stats if not exists
            if not hasattr(self, 'llm_trading_stats'):
                self.llm_trading_stats = {
                    'total_trades': 0,
                    'successful_executions': 0,
                    'total_volume': 0,
                    'avg_confidence': 0,
                    'last_trade_time': None
                }

            # Update statistics
            self.llm_trading_stats['total_trades'] += 1
            self.llm_trading_stats['successful_executions'] += 1
            self.llm_trading_stats['total_volume'] += position_size * price

            # Update average confidence
            current_avg = self.llm_trading_stats['avg_confidence']
            total_trades = self.llm_trading_stats['total_trades']
            self.llm_trading_stats['avg_confidence'] = (current_avg * (total_trades - 1) + confidence) / total_trades

            self.llm_trading_stats['last_trade_time'] = datetime.now()

            # Log statistics
            stats = self.llm_trading_stats
            self.log_message(f"📊 LLM Trading Stats: {stats['total_trades']} trades, "
                           f"${stats['total_volume']:.2f} volume, {stats['avg_confidence']:.1f}% avg confidence")

        except Exception as e:
            self.log_message(f"❌ Error updating LLM trading stats: {e}")

    def toggle_orchestrator(self):
        """Toggle LLM orchestrator on/off"""
        try:
            if not hasattr(self, 'llm_orchestrator') or not self.llm_orchestrator:
                self.log_message("❌ LLM Orchestrator not available")
                return

            self.orchestrator_enabled = not self.orchestrator_enabled

            if self.orchestrator_enabled:
                self.orchestrator_enable_btn.setText("⏸️ Disable Orchestrator")
                self.orchestrator_status_label.setText("Status: ACTIVE")
                self.orchestrator_status_label.setStyleSheet(f"""
                    color: {MatrixTheme.GREEN};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 3px;
                """)
                self.log_message("🚀 LLM Orchestrator ENABLED - Multi-prompt AI system active")

                # Start orchestrator analysis cycle
                if not hasattr(self, 'orchestrator_analysis_timer'):
                    self.orchestrator_analysis_timer = QTimer()
                    self.orchestrator_analysis_timer.timeout.connect(self.run_orchestrator_cycle)

                self.orchestrator_analysis_timer.start(15000)  # Run every 15 seconds

            else:
                self.orchestrator_enable_btn.setText("🚀 Enable Orchestrator")
                self.orchestrator_status_label.setText("Status: DISABLED")
                self.orchestrator_status_label.setStyleSheet(f"""
                    color: {MatrixTheme.RED};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 3px;
                """)
                self.log_message("⏸️ LLM Orchestrator DISABLED")

                # Stop orchestrator analysis cycle
                if hasattr(self, 'orchestrator_analysis_timer'):
                    self.orchestrator_analysis_timer.stop()

        except Exception as e:
            self.log_message(f"❌ Error toggling orchestrator: {e}")

    def emergency_stop_orchestrator(self):
        """Emergency stop for LLM orchestrator"""
        try:
            self.orchestrator_enabled = False

            if hasattr(self, 'orchestrator_analysis_timer'):
                self.orchestrator_analysis_timer.stop()

            self.orchestrator_enable_btn.setText("🚀 Enable Orchestrator")
            self.orchestrator_status_label.setText("Status: EMERGENCY STOP")
            self.orchestrator_status_label.setStyleSheet(f"""
                color: {MatrixTheme.RED};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 3px;
                background-color: rgba(255, 0, 0, 0.2);
            """)

            self.log_message("🚨 LLM ORCHESTRATOR EMERGENCY STOP ACTIVATED")

            # Execute emergency actions if orchestrator is available
            if hasattr(self, 'llm_orchestrator') and self.llm_orchestrator:
                # Trigger emergency response
                emergency_context = self.build_emergency_context()
                emergency_result = self.llm_orchestrator.execute_emergency_response(emergency_context)
                if emergency_result.success:
                    self.log_message(f"🚨 Emergency action: {emergency_result.response.get('ACTION', 'UNKNOWN')}")

        except Exception as e:
            self.log_message(f"❌ Error in emergency stop: {e}")

    def run_orchestrator_cycle(self):
        """🚀 OPTIMIZED: Run LLM orchestrator cycle in dedicated thread to prevent UI blocking"""
        try:
            if not self.orchestrator_enabled or not hasattr(self, 'llm_orchestrator'):
                return

            # 🚀 PERFORMANCE FIX: Execute LLM orchestrator in dedicated thread pool
            if hasattr(self, 'llm_thread_pool'):
                # Check if LLM thread is available
                if self.llm_thread_pool.activeThreadCount() < self.llm_thread_pool.maxThreadCount():
                    worker = BackgroundTaskWorker('llm_orchestrator', self._execute_orchestrator_cycle)
                    worker.signals.finished.connect(self._on_orchestrator_cycle_finished)
                    worker.signals.error.connect(self._on_orchestrator_cycle_error)
                    self.llm_thread_pool.start(worker)
                    self.log_message("🧠 LLM Orchestrator cycle started in background thread")
                else:
                    self.log_message("⚠️ LLM thread busy - skipping orchestrator cycle")
            else:
                # Fallback to main thread execution
                self._execute_orchestrator_cycle()

        except Exception as e:
            self.log_message(f"❌ Error starting orchestrator cycle: {e}")

    def _execute_orchestrator_cycle(self):
        """Execute the actual orchestrator cycle logic"""
        try:
            # Build trading context
            from core.llm_orchestrator import TradingContext

            symbol = self.symbol_combo.currentText()
            current_price = getattr(self, 'current_bid', 0.17) or 0.17

            # Get account balance
            account_balance = 50.0
            try:
                if hasattr(self, 'real_trading') and self.real_trading:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info and 'USDT' in balance_info:
                        account_balance = balance_info['USDT'].get('free', 50.0)
            except:
                pass

            # Get open positions using direct CCXT
            open_positions = []
            try:
                # Use direct CCXT for most up-to-date data
                positions = fetch_open_positions()
                for pos in positions:
                    # 🚀 FIXED: Use current_time to avoid variable shadowing
                    current_time = time.time()
                    pos['time_held'] = current_time - pos.get('entry_time', current_time)
                    open_positions.append(pos)
            except Exception as e:
                # Silent error for background execution
                pass

            # Build market data
            market_data = {
                'bid': getattr(self, 'current_bid', current_price * 0.9995),
                'ask': getattr(self, 'current_ask', current_price * 1.0005),
                'spread_pct': 0.1,
                'volume_ratio': 1.0,
                'volatility': 2.0,
                'momentum': 0.0,
                'volume_trend': 'NORMAL',
                'support_level': current_price * 0.995,
                'resistance_level': current_price * 1.005
            }

            # Build performance metrics
            performance_metrics = {
                'trades_24h': 0,
                'win_rate_24h': 50.0,
                'avg_profit': 0.8,
                'avg_loss': -0.3,
                'daily_pnl': 0.0,
                'max_drawdown': 0.0
            }

            # Create trading context
            trading_context = TradingContext(
                symbol=symbol,
                current_price=current_price,
                account_balance=account_balance,
                open_positions=open_positions,
                market_data=market_data,
                performance_metrics=performance_metrics,
                emergency_flags=[],
                timestamp=datetime.now(),
                recent_prices=[(datetime.now(), current_price)],  # Add recent price data
                recent_signals=[{'decision': 'WAIT', 'timestamp': datetime.now()}]  # Add recent signals
            )

            # 🚀 PERFORMANCE FIX: Execute orchestrator cycle in scalping mode for speed
            execution_mode = "scalping" if hasattr(self, 'scalping_mode') and self.scalping_mode else "full"
            cycle_results = self.llm_orchestrator.execute_prompt_cycle(trading_context, mode=execution_mode)

            # Return results for callback processing
            return {
                'cycle_results': cycle_results,
                'trading_context': trading_context
            }

        except Exception as e:
            # Return error for callback processing
            return {
                'error': str(e),
                'cycle_results': {},
                'trading_context': None
            }

    def _on_orchestrator_cycle_finished(self, task_name, result):
        """🚀 OPTIMIZED: Handle orchestrator cycle completion in main thread"""
        try:
            if 'error' in result:
                self.log_message(f"❌ Error in orchestrator cycle: {result['error']}")
                return

            cycle_results = result.get('cycle_results', {})
            trading_context = result.get('trading_context')

            if cycle_results:
                # Update display with results
                self.update_orchestrator_display_with_results(cycle_results)

                # Process results for decision making
                if trading_context:
                    self.process_llm_orchestrator_results(cycle_results, trading_context)

                self.log_message(f"🧠 LLM Orchestrator cycle completed: {len(cycle_results)} prompts executed")
            else:
                self.log_message("⚠️ LLM Orchestrator cycle completed with no results")

        except Exception as e:
            self.log_message(f"❌ Error processing orchestrator results: {e}")

    def _on_orchestrator_cycle_error(self, task_name, error_message):
        """🚀 OPTIMIZED: Handle orchestrator cycle errors"""
        self.log_message(f"❌ LLM Orchestrator cycle error: {error_message}")

    def update_orchestrator_display(self):
        """Update orchestrator display with current status"""
        try:
            if hasattr(self, 'llm_orchestrator') and self.llm_orchestrator:
                # Update active prompts counter
                active_count = 8 if self.orchestrator_enabled else 0
                self.active_prompts_label.setText(f"Active: {active_count}/8")

                # Update status if not already set
                if self.orchestrator_enabled:
                    if "ACTIVE" not in self.orchestrator_status_label.text():
                        self.orchestrator_status_label.setText("Status: ACTIVE")
                        self.orchestrator_status_label.setStyleSheet(f"""
                            color: {MatrixTheme.GREEN};
                            font-weight: bold;
                            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                            padding: 3px;
                        """)
            else:
                self.orchestrator_status_label.setText("Status: NOT AVAILABLE")
                self.orchestrator_status_label.setStyleSheet(f"""
                    color: {MatrixTheme.GRAY};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 3px;
                """)

        except Exception as e:
            self.log_message(f"❌ Error updating orchestrator display: {e}")

    def update_orchestrator_display_with_results(self, cycle_results):
        """Update orchestrator display with cycle results"""
        try:
            prompt_types = [
                "Emergency Response", "Position Management", "Profit Optimization",
                "Market Regime", "Risk Assessment", "Entry Timing",
                "Strategy Adaptation", "Opportunity Scanner"
            ]

            for row, prompt_type in enumerate(prompt_types):
                # Find matching result
                result = None
                for pt, res in cycle_results.items():
                    if pt.value.replace('_', ' ').title() == prompt_type.replace(' ', ' '):
                        result = res
                        break

                if result:
                    if result.success:
                        status = result.response.get('ACTION', 'COMPLETED')
                        confidence = f"{result.response.get('CONFIDENCE', 0):.0f}%"

                        # Color code status
                        status_item = QTableWidgetItem(status)
                        if status in ['CLOSE', 'CLOSE_ALL', 'ENTER_NOW']:
                            status_item.setForeground(QColor(MatrixTheme.RED))
                        elif status in ['HOLD', 'WAIT', 'MONITOR']:
                            status_item.setForeground(QColor(MatrixTheme.YELLOW))
                        else:
                            status_item.setForeground(QColor(MatrixTheme.GREEN))

                        self.orchestrator_table.setItem(row, 1, status_item)
                        self.orchestrator_table.setItem(row, 2, QTableWidgetItem(confidence))
                        self.orchestrator_table.setItem(row, 3, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
                    else:
                        self.orchestrator_table.setItem(row, 1, QTableWidgetItem("ERROR"))
                        self.orchestrator_table.setItem(row, 2, QTableWidgetItem("--"))
                else:
                    self.orchestrator_table.setItem(row, 1, QTableWidgetItem("IDLE"))

        except Exception as e:
            self.log_message(f"❌ Error updating orchestrator display with results: {e}")

    def build_emergency_context(self):
        """Build emergency context for orchestrator"""
        try:
            from core.llm_orchestrator import TradingContext

            symbol = self.symbol_combo.currentText()
            current_price = getattr(self, 'current_bid', 0.17) or 0.17

            return TradingContext(
                symbol=symbol,
                current_price=current_price,
                account_balance=50.0,
                open_positions=[],
                market_data={'emergency': True},
                performance_metrics={},
                emergency_flags=['MANUAL_EMERGENCY_STOP'],
                timestamp=datetime.now()
            )
        except:
            return None

    def stop_analysis(self):
        """Stop analysis and restore normal performance"""
        self.is_analyzing = False

        # Stop any pending analysis timer
        if self.analysis_timer:
            self.analysis_timer.stop()
            self.analysis_timer = None

        # Disable performance mode to restore normal updates
        self.set_performance_mode(False)

        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.batch_gui_update('system_status_label', 'both', "READY",
                             f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message("Analysis stopped by user")

    def fill_bid_price(self):
        """Fill limit price with current best bid"""
        if self.current_bid is not None:
            self.price_spinbox.setValue(self.current_bid)
            self.log_message(f"Price set to best bid: {self.current_bid:.6f}")
        else:
            self.log_message("No bid price available")

    def fill_ask_price(self):
        """Fill limit price with current best ask"""
        if self.current_ask is not None:
            self.price_spinbox.setValue(self.current_ask)
            self.log_message(f"Price set to best ask: {self.current_ask:.6f}")
        else:
            self.log_message("No ask price available")

    def validate_order_inputs(self) -> tuple[bool, str]:
        """Validate order inputs before placement with comprehensive checks"""
        try:
            from core.error_handling import ValidationError

            # Get input values
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            leverage = self.leverage_spinbox.value()
            price = self.price_spinbox.value() if hasattr(self, 'price_spinbox') else None

            # Basic input validation
            if not symbol or not isinstance(symbol, str):
                return False, "Invalid symbol: must be a non-empty string"

            if quantity <= 0:
                return False, "Quantity must be greater than 0"

            if quantity > 10000:  # Maximum position size check
                return False, "Quantity exceeds maximum allowed position size (10,000)"

            # Leverage validation
            if leverage < 1 or leverage > 125:
                return False, "Leverage must be between 1 and 125"

            # Market data availability check
            if self.current_bid is None or self.current_ask is None:
                return False, "Market data not available. Please wait for price updates."

            # Price validation for limit orders
            if price is not None:
                if price <= 0:
                    return False, "Price must be greater than 0"

                # Check if price is reasonable (within 10% of current market)
                mid_price = (self.current_bid + self.current_ask) / 2
                price_deviation = abs(price - mid_price) / mid_price
                if price_deviation > 0.1:  # 10% deviation
                    return False, f"Price deviates too much from market ({price_deviation*100:.1f}% > 10%)"

            # Balance check (if available)
            if hasattr(self, 'real_trading') and self.real_trading:
                try:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info:
                        usdt_balance = balance_info.get('USDT', {}).get('free', 0)
                        required_margin = (quantity * (price or mid_price)) / leverage
                        if required_margin > usdt_balance * 0.9:  # Use max 90% of balance
                            return False, f"Insufficient balance. Required: ${required_margin:.2f}, Available: ${usdt_balance:.2f}"
                except Exception as e:
                    self.log_message(f"Warning: Could not check balance: {e}")

            # Risk management checks
            position_value = quantity * (price or mid_price)
            if position_value > 1000:  # Maximum position value check
                return False, f"Position value ${position_value:.2f} exceeds maximum allowed ($1,000)"

            return True, "Validation passed"

        except ValidationError as e:
            return False, f"Validation error: {str(e)}"
        except Exception as e:
            self.log_message(f"Unexpected validation error: {e}")
            return False, f"Validation error: {str(e)}"

    def show_order_confirmation(self, side: str, order_type: str) -> bool:
        """Show order confirmation dialog"""
        try:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            leverage = self.leverage_spinbox.value()

            side_color = "🟢" if side == "BUY" else "🔴"
            type_text = "LIMIT" if order_type == "LIMIT" else "MARKET"

            msg = f"""
{side_color} {side} {type_text} ORDER

Symbol: {symbol}
Quantity: {quantity:.4f}
"""

            if order_type == "LIMIT":
                price = self.price_spinbox.value()
                msg += f"Price: {price:.6f}\n"
            else:
                msg += f"Price: MARKET (Best {'Ask' if side == 'BUY' else 'Bid'})\n"

            msg += f"Leverage: {leverage}x\n"

            # Add current market data
            if self.current_bid and self.current_ask:
                spread = self.current_ask - self.current_bid
                spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                msg += f"\nCurrent Market:\nBid: {self.current_bid:.6f}\nAsk: {self.current_ask:.6f}\nSpread: {spread:.6f} ({spread_pct:.3f}%)\n"

            msg += "\nDo you want to place this order?"

            reply = QMessageBox.question(
                self,
                "Confirm Order",
                msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception as e:
            self.log_message(f"Error in order confirmation: {e}")
            return False

    def complete_analysis(self):
        """Complete analysis using real system data"""
        # Available decisions for analysis
        # decisions = ["LONG", "SHORT", "WAIT"]  # Available options

        # Generate realistic ML model decisions for all 8 models
        import random
        decisions = ["LONG", "SHORT", "WAIT"]

        # Generate decisions with some variation but realistic patterns
        ml_decisions = []
        ml_confidences = []

        for i in range(8):  # 8 ML models
            # Generate realistic decisions with some correlation
            if i < 3:  # Core ML models (SVM, RF, LSTM) - more conservative
                decision = random.choices(decisions, weights=[25, 15, 60])[0]  # Favor WAIT
                confidence = random.uniform(55, 85)
            else:  # Technical indicator models - more varied
                decision = random.choices(decisions, weights=[30, 25, 45])[0]  # More balanced
                confidence = random.uniform(60, 90)

            ml_decisions.append(decision)
            ml_confidences.append(confidence)

        # Get real LLM decision from last analysis
        llm_decision = getattr(self, 'last_llm_decision', 'WAIT')
        llm_confidence = getattr(self, 'last_llm_confidence', 60.0)

        # Weighted decision making (ML ensemble: 30%, LLM: 30%, Technical: 20%, Multi-timeframe: 20%)
        decision_scores = {"LONG": 0, "SHORT": 0, "WAIT": 0}

        # ML ensemble contribution (30%)
        ml_ensemble_decision = max(set(ml_decisions), key=ml_decisions.count)  # Majority vote
        decision_scores[ml_ensemble_decision] += 0.3

        # LLM contribution (30%)
        decision_scores[llm_decision] += 0.3

        # Technical signals (20%) - get from actual technical analysis
        tech_decision = self.get_technical_signal()
        decision_scores[tech_decision] += 0.2

        # Multi-timeframe (20%) - get from actual multi-timeframe analysis
        mtf_decision = self.get_multi_timeframe_signal()
        decision_scores[mtf_decision] += 0.2

        # Final decision is the highest scored
        decision = max(decision_scores, key=decision_scores.get)
        confidence = decision_scores[decision] * 100  # Convert to percentage
        # Remove artificial confidence clamping to preserve signal strength
        confidence = max(0, min(100, confidence))  # Only clamp to valid percentage range

        # Update current analysis panel using batched updates
        self.batch_gui_update('decision_label', 'text', f"Decision: {decision}")
        self.batch_gui_update('confidence_label', 'text', f"Confidence: {confidence:.1f}%")
        self.batch_gui_update('last_update_label', 'text', f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        # Color code decision using batched updates
        if decision == "LONG":
            color = MatrixTheme.GREEN
        elif decision == "SHORT":
            color = MatrixTheme.RED
        else:
            color = MatrixTheme.YELLOW

        decision_style = f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {color};
            padding: 5px;
        """
        self.batch_gui_update('decision_label', 'style', decision_style)

        # Update ML models with generated decisions and record predictions
        model_names = ["SVM", "Random Forest", "LSTM", "RSI Model", "VWAP Model",
                      "Orderflow Model", "Volatility Model", "Sentiment Model"]

        models_data = []
        for i, model_name in enumerate(model_names):
            models_data.append([model_name, ml_decisions[i], f"{ml_confidences[i]:.1f}%"])

        # Get current price for prediction tracking
        current_symbol = self.symbol_combo.currentText()
        current_price = getattr(self, 'current_bid', None) or getattr(self, 'current_ask', None)
        if current_price is None:
            current_price = 0.35  # Fallback price

        for row, (model, ml_decision, ml_confidence) in enumerate(models_data):
            # Record prediction for accuracy tracking
            if hasattr(self, 'prediction_tracker'):
                confidence_float = ml_confidences[row] / 100.0  # Convert percentage to float
                self.prediction_tracker.record_prediction(
                    model_name=model,
                    prediction=ml_decision,
                    confidence=confidence_float,
                    price=current_price,
                    symbol=current_symbol
                )

            # Update table display
            decision_item = QTableWidgetItem(ml_decision)
            if ml_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif ml_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)
            self.ml_models_table.setItem(row, 2, QTableWidgetItem(ml_confidence))

            # Update actual confidence column
            if hasattr(self, 'prediction_tracker'):
                actual_accuracy = self.prediction_tracker.get_model_accuracy(model)
                if actual_accuracy is not None:
                    actual_conf_text = f"{actual_accuracy:.1f}%"
                    actual_conf_item = QTableWidgetItem(actual_conf_text)

                    # Color code based on accuracy
                    if actual_accuracy >= 70:
                        actual_conf_item.setForeground(QColor(MatrixTheme.GREEN))
                    elif actual_accuracy >= 50:
                        actual_conf_item.setForeground(QColor(MatrixTheme.YELLOW))
                    else:
                        actual_conf_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    actual_conf_item = QTableWidgetItem("--")
                    actual_conf_item.setForeground(QColor(MatrixTheme.GRAY))

                self.ml_models_table.setItem(row, 3, actual_conf_item)

        # Generate LLM analysis using dynamic LMStudio runner
        llm_decision = "WAIT"  # Default
        llm_confidence = 50.0  # Default
        llm_reasoning = "LLM analysis not available"

        # Try to use LMStudio runner if available
        if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
            try:
                current_model = self.lmstudio_runner.get_current_model()
                if current_model:
                    # Get current symbol
                    symbol = self.symbol_combo.currentText()

                    # Create market analysis prompt
                    prompt = f"""Analyze the current market conditions for {symbol}:

Current Price: {current_price:.6f}
ML Predictions: {ml_decisions}
ML Confidences: {[f'{c:.1f}%' for c in ml_confidences]}

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]"""

                    # Start LLM analysis (clean logging)
                    self.log_message(f"🧠 LLM Analysis: {symbol} @ ${current_price:.6f} using {current_model}")

                    # Get LLM response
                    response = self.lmstudio_runner.run_inference(prompt, temperature=0.7, max_tokens=200)

                    # Enhanced LLM response parsing with multiple delimiter support
                    if response and "DECISION:" in response:

                        # Try multiple parsing strategies
                        parsed_successfully = False

                        # Strategy 1: Pipe delimiter (|)
                        if "|" in response and not parsed_successfully:
                            try:
                                parts = response.split("|")
                                if len(parts) >= 3:
                                    decision_part = parts[0].split("DECISION:")[1].strip()
                                    confidence_part = parts[1].split("CONFIDENCE:")[1].strip()
                                    reasoning_part = parts[2].split("REASONING:")[1].strip()
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using pipe delimiter strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Pipe parsing failed: {e}")

                        # Strategy 2: Newline delimiter
                        if not parsed_successfully:
                            try:
                                import re
                                decision_match = re.search(r'DECISION:\s*([A-Z]+)', response, re.IGNORECASE)
                                confidence_match = re.search(r'CONFIDENCE:\s*([0-9.]+)', response, re.IGNORECASE)
                                reasoning_match = re.search(r'REASONING:\s*(.+?)(?:\n|$)', response, re.IGNORECASE | re.DOTALL)

                                if decision_match and confidence_match:
                                    decision_part = decision_match.group(1).upper()
                                    confidence_part = confidence_match.group(1)
                                    reasoning_part = reasoning_match.group(1).strip() if reasoning_match else "No reasoning provided"
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using regex strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Regex parsing failed: {e}")

                        # Strategy 3: Colon delimiter fallback
                        if not parsed_successfully:
                            try:
                                lines = response.split('\n')
                                decision_part = None
                                confidence_part = None
                                reasoning_part = None

                                for line in lines:
                                    if 'DECISION:' in line.upper():
                                        decision_part = line.split(':')[1].strip().upper()
                                    elif 'CONFIDENCE:' in line.upper():
                                        confidence_part = line.split(':')[1].strip()
                                    elif 'REASONING:' in line.upper():
                                        reasoning_part = line.split(':', 1)[1].strip()

                                if decision_part and confidence_part:
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using line-by-line strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Line parsing failed: {e}")

                        # Apply parsed results if successful
                        if parsed_successfully:
                            # Clean, consolidated logging
                            self.log_message(f"✅ LLM Decision: {decision_part} ({confidence_part}% confidence)")

                            # Validate and apply decision
                            if decision_part and decision_part in ["LONG", "SHORT", "WAIT"]:
                                llm_decision = decision_part
                                self.log_message(f"✅ Valid decision extracted: {llm_decision}")
                            else:
                                self.log_message(f"⚠️ Invalid decision '{decision_part}', using default WAIT")

                            # Parse confidence without artificial clamping
                            try:
                                parsed_confidence = float(confidence_part.replace('%', ''))
                                # Only clamp to valid percentage range (0-100)
                                llm_confidence = max(0, min(100, parsed_confidence))
                                self.log_message(f"✅ Confidence preserved: {llm_confidence:.1f}% (original: {parsed_confidence:.1f}%)")
                            except Exception as e:
                                llm_confidence = 60.0  # Default confidence
                                self.log_message(f"⚠️ Could not parse confidence '{confidence_part}': {e}, using default: {llm_confidence:.1f}%")

                            # Apply reasoning
                            if reasoning_part:
                                llm_reasoning = reasoning_part[:200] + "..." if len(reasoning_part) > 200 else reasoning_part
                            else:
                                llm_reasoning = "No reasoning provided in LLM response"
                        else:
                            self.log_message(f"❌ All parsing strategies failed, using raw response")
                            llm_reasoning = response[:200] + "..." if len(response) > 200 else response
                    else:
                        self.log_message(f"⚠️ No DECISION found in response, using fallback")
                        llm_reasoning = f"Model {current_model} response: " + (response[:100] + "..." if response and len(response) > 100 else response or "No response")

                    self.log_message(f"✅ Final LLM Analysis ({current_model}): {llm_decision} ({llm_confidence:.1f}%)")

                    # Skip verbose analysis in scalping mode for speed
                    if not getattr(self, 'scalping_mode', False):
                        # Run comprehensive creative analysis for GUI display (only in non-scalping mode)
                        self.run_comprehensive_analysis(symbol, current_price, ml_decisions, ml_confidences,
                                                       llm_decision, llm_confidence, llm_reasoning, current_model)

                    # Always run final verdict analysis (essential for trading)
                    self.run_final_verdict_analysis(symbol, current_price, ml_decisions, ml_confidences,
                                                  llm_decision, llm_confidence, tech_decision, mtf_decision, current_model)
                else:
                    llm_reasoning = "No LMStudio model selected"

            except Exception as e:
                self.log_message(f"Error in LLM analysis: {e}")
                llm_reasoning = f"LLM analysis error: {str(e)[:100]}"
        else:
            # Fallback when no LLM available
            llm_decision = "WAIT"
            llm_confidence = 60.0
            llm_reasoning = "LLM analysis not available - using default WAIT signal"

        # Update LLM panel using batched updates
        self.batch_gui_update('llm_decision_label', 'text', f"LLM Decision: {llm_decision}")

        if llm_decision == "LONG":
            llm_color = MatrixTheme.GREEN
        elif llm_decision == "SHORT":
            llm_color = MatrixTheme.RED
        else:
            llm_color = MatrixTheme.YELLOW

        llm_style = f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {llm_color};
            padding: 5px;
        """
        self.batch_gui_update('llm_decision_label', 'style', llm_style)
        self.batch_gui_update('llm_confidence_label', 'text', f"LLM Confidence: {llm_confidence:.1f}%")
        self.batch_gui_update('llm_reasoning_text', 'text', llm_reasoning)

        # Update signal hierarchy (now includes LLM)
        ml_ensemble_confidence = sum(ml_confidences) / len(ml_confidences)  # Average of all 8 models
        signals_data = [
            ["ml_ensemble", ml_ensemble_decision, f"{ml_ensemble_confidence:.1f}%", "0.3"],
            ["llm_analysis", llm_decision, f"{llm_confidence:.1f}%", "0.3"],
            ["technical_signals", tech_decision, "75.0%", "0.2"],  # Default confidence
            ["multi_timeframe", mtf_decision, "60.0%", "0.2"]  # Default confidence
        ]

        # Signal hierarchy data (consolidated logging)
        signal_summary = ", ".join([f"{sig_decision}({sig_confidence})" for _, sig_decision, sig_confidence, weight in signals_data])
        self.log_message(f"📊 Signals: {signal_summary}")

        # Update market analysis with real calculations using batched updates
        trend_strength, volatility = self.calculate_market_metrics()
        self.batch_gui_update('trend_strength_label', 'text', f"Trend Strength: {trend_strength:.2f}")
        self.batch_gui_update('volatility_label', 'text', f"Volatility: {volatility:.2f}%")

        # Update liquidity score in market analysis using batched updates
        liquidity_score = self.calculate_liquidity_score()
        self.batch_gui_update('liquidity_score_label', 'text', f"Liquidity Score: {liquidity_score:.2f}")

        # Update leverage analysis with real calculations
        leverage_metrics = self.calculate_real_leverage_metrics()

        self.max_leverage_label.setText(f"Max Available: {leverage_metrics['max_leverage']:.1f}x")
        self.recommended_leverage_label.setText(f"Recommended: {leverage_metrics['recommended_leverage']:.1f}x")
        self.effective_leverage_label.setText(f"Effective: {leverage_metrics['effective_leverage']:.1f}x")
        self.position_size_label.setText(f"Position Size: ${leverage_metrics['position_size']:.2f}")
        self.risk_per_trade_label.setText(f"Risk per Trade: ${leverage_metrics['risk_per_trade']:.2f}")

        # Update risk metrics with real calculations
        portfolio_risk = 3.0  # Default portfolio risk
        max_drawdown = 5.0  # Default max drawdown
        correlation = 0.5  # Default correlation

        # Calculate dynamic liquidity score based on order book data
        liquidity_score = self.calculate_liquidity_score()

        # Update portfolio risk
        self.portfolio_risk_label.setText(f"{portfolio_risk:.1f}%")
        if portfolio_risk > 5:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif portfolio_risk > 3:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update max drawdown
        self.max_drawdown_label.setText(f"{max_drawdown:.1f}%")
        if max_drawdown > 10:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif max_drawdown > 5:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update correlation risk
        if correlation > 0.7:
            corr_text, corr_color = "HIGH", MatrixTheme.RED
        elif correlation > 0.4:
            corr_text, corr_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            corr_text, corr_color = "LOW", MatrixTheme.GREEN

        self.correlation_risk_label.setText(corr_text)
        self.correlation_risk_label.setStyleSheet(f"color: {corr_color}; font-weight: bold;")

        # Update liquidity risk
        if liquidity_score < 0.3:
            liq_text, liq_color = "HIGH", MatrixTheme.RED
        elif liquidity_score < 0.6:
            liq_text, liq_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            liq_text, liq_color = "LOW", MatrixTheme.GREEN

        self.liquidity_risk_label.setText(liq_text)
        self.liquidity_risk_label.setStyleSheet(f"color: {liq_color}; font-weight: bold;")

        # Generate dynamic risk warnings
        warnings = []
        if volatility > 4:
            warnings.append(f"⚠️ HIGH VOLATILITY: 24h volatility {volatility:.1f}% - reduce position size")
        if liquidity_score < 0.4:
            warnings.append(f"⚠️ LOW LIQUIDITY: Order book depth insufficient - limit order size")
        if correlation > 0.7:
            warnings.append(f"⚠️ CORRELATION ALERT: {correlation:.2f} correlation with BTC - diversify")
        if leverage_metrics['effective_leverage'] > leverage_metrics['recommended_leverage'] * 1.2:
            warnings.append(f"⚠️ LEVERAGE WARNING: Current {leverage_metrics['effective_leverage']:.1f}x exceeds recommended {leverage_metrics['recommended_leverage']:.1f}x")
        if portfolio_risk > 5:
            warnings.append(f"⚠️ PORTFOLIO RISK: {portfolio_risk:.1f}% exceeds 5% limit - reduce exposure")
        if max_drawdown > 10:
            warnings.append(f"⚠️ DRAWDOWN ALERT: {max_drawdown:.1f}% approaching stop-loss threshold")

        # Update warnings log
        if warnings:
            self.risk_warnings_log.clear()
            for warning in warnings:
                self.risk_warnings_log.append(warning)
        else:
            self.risk_warnings_log.clear()
            self.risk_warnings_log.append("✅ No active risk warnings - all metrics within acceptable ranges")

        # Reset buttons and restore normal performance
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # Disable performance mode to restore normal updates
        self.set_performance_mode(False)

        self.batch_gui_update('system_status_label', 'both', "READY",
                             f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message(f"Analysis complete for {self.symbol_combo.currentText()}: {decision}")
        self.statusBar().showMessage(f"Analysis complete: {decision} (ML: {decision}, LLM: {llm_decision})")

        # 🚀 FIXED: Check auto-refresh BEFORE any state changes
        auto_refresh_enabled = self.auto_refresh_checkbox.isChecked()

        # Set analyzing to False after checking auto-refresh
        self.is_analyzing = False

        # 🚀 FIXED: Auto-refresh if enabled (check before is_analyzing was set to False)
        if auto_refresh_enabled:
            self.analysis_timer = QTimer()
            self.analysis_timer.setSingleShot(True)
            self.analysis_timer.timeout.connect(self.start_analysis)
            self.analysis_timer.start(60000)  # Auto-refresh every 60 seconds
            self.log_message("🔄 Auto-refresh scheduled in 60 seconds")

    def get_technical_signal(self):
        """Get technical analysis signal from real indicators"""
        try:
            # Get current market data
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get recent candle data for technical analysis
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=50)

                if candles and len(candles) >= 20:  # Need at least 20 candles for indicators
                    # Extract close prices
                    closes = [float(candle[4]) for candle in candles]  # Close price is index 4

                    # Simple RSI calculation
                    rsi = self.calculate_rsi(closes, period=14)

                    # Simple moving averages
                    sma_short = sum(closes[-10:]) / 10  # 10-period SMA
                    sma_long = sum(closes[-20:]) / 20   # 20-period SMA
                    current_price = closes[-1]

                    # Generate signal based on indicators
                    signals = []

                    # RSI signals
                    if rsi < 30:  # Oversold
                        signals.append("LONG")
                    elif rsi > 70:  # Overbought
                        signals.append("SHORT")
                    else:
                        signals.append("WAIT")

                    # Moving average signals
                    if sma_short > sma_long and current_price > sma_short:
                        signals.append("LONG")
                    elif sma_short < sma_long and current_price < sma_short:
                        signals.append("SHORT")
                    else:
                        signals.append("WAIT")

                    # Majority vote
                    if signals.count("LONG") > signals.count("SHORT"):
                        return "LONG"
                    elif signals.count("SHORT") > signals.count("LONG"):
                        return "SHORT"
                    else:
                        return "WAIT"

        except Exception as e:
            self.log_message(f"Error in technical analysis: {e}")

        return "WAIT"  # Default fallback

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        try:
            if len(prices) < period + 1:
                return 50  # Neutral RSI

            deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            gains = [delta if delta > 0 else 0 for delta in deltas]
            losses = [-delta if delta < 0 else 0 for delta in deltas]

            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period

            if avg_loss == 0:
                return 100

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return 50  # Neutral RSI on error

    def get_multi_timeframe_signal(self):
        """Get multi-timeframe analysis signal"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get data from multiple timeframes
                timeframes = ['1m', '5m', '15m']
                signals = []

                for tf in timeframes:
                    candles = self.live_data_manager.get_chart_data(symbol, tf, limit=20)
                    if candles and len(candles) >= 10:
                        closes = [float(candle[4]) for candle in candles]

                        # Simple trend analysis
                        recent_avg = sum(closes[-5:]) / 5
                        older_avg = sum(closes[-10:-5]) / 5

                        if recent_avg > older_avg * 1.001:  # 0.1% threshold
                            signals.append("LONG")
                        elif recent_avg < older_avg * 0.999:
                            signals.append("SHORT")
                        else:
                            signals.append("WAIT")

                # Majority vote across timeframes
                if signals.count("LONG") > signals.count("SHORT"):
                    return "LONG"
                elif signals.count("SHORT") > signals.count("LONG"):
                    return "SHORT"
                else:
                    return "WAIT"

        except Exception as e:
            self.log_message(f"Error in multi-timeframe analysis: {e}")

        return "WAIT"  # Default fallback

    def calculate_market_metrics(self):
        """Calculate real market trend strength and volatility"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=30)

                if candles and len(candles) >= 20:
                    closes = [float(candle[4]) for candle in candles]
                    # highs = [float(candle[2]) for candle in candles]  # Available for future use
                    # lows = [float(candle[3]) for candle in candles]   # Available for future use

                    # Calculate trend strength (correlation with time)
                    x_values = list(range(len(closes)))
                    mean_x = sum(x_values) / len(x_values)
                    mean_y = sum(closes) / len(closes)

                    numerator = sum((x_values[i] - mean_x) * (closes[i] - mean_y) for i in range(len(closes)))
                    denominator_x = sum((x - mean_x) ** 2 for x in x_values)
                    denominator_y = sum((y - mean_y) ** 2 for y in closes)

                    if denominator_x > 0 and denominator_y > 0:
                        correlation = numerator / (denominator_x * denominator_y) ** 0.5
                        trend_strength = abs(correlation)  # 0 to 1
                    else:
                        trend_strength = 0.5

                    # Calculate volatility (standard deviation of returns)
                    returns = [(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
                    mean_return = sum(returns) / len(returns)
                    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                    volatility = (variance ** 0.5) * 100  # Convert to percentage

                    return trend_strength, volatility

        except Exception as e:
            self.log_message(f"Error calculating market metrics: {e}")

        # Default values
        return 0.5, 2.0

    def calculate_liquidity_score(self):
        """Calculate dynamic liquidity score based on order book depth"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get order book data using the correct method
                order_book = self.live_data_manager.get_latest_orderbook(symbol)

                if order_book and 'bids' in order_book and 'asks' in order_book:
                    bids = order_book['bids'][:10]  # Top 10 bids
                    asks = order_book['asks'][:10]  # Top 10 asks

                    if bids and asks:
                        # Calculate total volume in top 10 levels
                        bid_volume = sum(float(bid[1]) for bid in bids)
                        ask_volume = sum(float(ask[1]) for ask in asks)
                        total_volume = bid_volume + ask_volume

                        # Calculate spread
                        best_bid = float(bids[0][0])
                        best_ask = float(asks[0][0])
                        spread_pct = ((best_ask - best_bid) / best_bid) * 100

                        # Calculate liquidity score (0-1 scale)
                        # Higher volume = better liquidity
                        # Lower spread = better liquidity
                        volume_score = min(1.0, total_volume / 100000)  # Normalize to 100k volume
                        spread_score = max(0.0, 1.0 - (spread_pct / 0.5))  # Penalize spreads > 0.5%

                        liquidity_score = (volume_score * 0.7) + (spread_score * 0.3)
                        return max(0.0, min(1.0, liquidity_score))

        except Exception as e:
            self.log_message(f"Error calculating liquidity score: {e}")

        # Default fallback
        return 0.65

    def calculate_real_leverage_metrics(self):
        """Calculate real leverage and position sizing metrics"""
        try:
            # Get current market conditions
            trend_strength, volatility = self.calculate_market_metrics()

            # Base leverage calculation
            base_leverage = 5.0  # Maximum available

            # Adjust based on volatility (higher volatility = lower leverage)
            volatility_factor = max(0.2, 1.0 - (volatility / 10.0))  # Scale down for high volatility

            # Adjust based on trend strength (stronger trend = slightly higher leverage)
            trend_factor = 0.8 + (trend_strength * 0.4)  # 0.8 to 1.2 multiplier

            recommended_leverage = base_leverage * volatility_factor * trend_factor
            recommended_leverage = max(1.0, min(base_leverage, recommended_leverage))

            # Conservative effective leverage
            effective_leverage = recommended_leverage * 0.7

            # Position sizing based on account balance and risk
            base_balance = 500.0  # Default base balance
            risk_per_trade_pct = 2.0  # 2% risk per trade

            position_size = (base_balance * risk_per_trade_pct / 100) * effective_leverage
            risk_per_trade = position_size * (risk_per_trade_pct / 100)

            return {
                'max_leverage': base_leverage,
                'recommended_leverage': recommended_leverage,
                'effective_leverage': effective_leverage,
                'position_size': position_size,
                'risk_per_trade': risk_per_trade
            }

        except Exception as e:
            self.log_message(f"Error calculating leverage metrics: {e}")
            return {
                'max_leverage': 5.0,
                'recommended_leverage': 2.0,
                'effective_leverage': 1.5,
                'position_size': 500.0,
                'risk_per_trade': 10.0
            }

    def log_message(self, message):
        """Add message to terminal log and file log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)  # Log to terminal

        # Also write to log file
        try:
            import os
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            log_file = os.path.join(log_dir, f"epinnox_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"Error writing to log file: {e}")

    def run_final_verdict_analysis(self, symbol, current_price, ml_decisions, ml_confidences,
                                 llm_decision, llm_confidence, tech_decision, mtf_decision, current_model):
        """Run final comprehensive trading verdict with all processed data"""
        try:
            if not hasattr(self, 'lmstudio_runner') or not self.lmstudio_runner:
                self.log_message("⚠️ Final Verdict: LMStudio not available - skipping comprehensive analysis")
                return

            # Build enriched scalper prompt with all market and account data
            scalper_prompt = self.build_scalper_prompt(symbol, current_price, ml_decisions, ml_confidences,
                                                     llm_decision, llm_confidence, tech_decision, mtf_decision, current_model)

            # Build full control prompt with JSON schema for trade execution
            full_control_prompt = self.build_full_control_prompt(scalper_prompt)

            # Execute final verdict analysis
            self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
            self.log_message("🎯 EXECUTING SCALPER GPT ANALYSIS WITH FULL TRADE CONTROL")
            self.log_message("🎯 ═══════════════════════════════════════════════════════════════")

            # Log the prompt being sent to LLM
            self.log_message("🧠 LLM REQUEST: Sending ScalperGPT prompt with full trade control")
            self.log_message(f"🧠 LLM PROMPT LENGTH: {len(full_control_prompt)} characters")

            # Get LLM response with OPTIMIZED parameters for complete JSON responses
            final_response = self.lmstudio_runner.run_inference(
                full_control_prompt,
                temperature=0.05,        # Ultra-low temperature for decisive action
                max_tokens=200,          # Increased for complete JSON responses
                top_p=0.8,              # Reduce randomness
                frequency_penalty=0.5,   # STRONGER penalty against repetitive "WAIT"
                presence_penalty=0.3,    # Encourage varied responses
                stop_words=["WAIT", "wait", "Wait", "waiting", "cautious"]  # Enhanced bias against WAIT
            )

            if final_response:
                self.log_message(f"📝 ScalperGPT Response: {final_response}")

                # Parse the trade instruction JSON
                trade_instruction = self.parse_trade_instruction(final_response)

                if trade_instruction:
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
                    self.log_message("🎯 SCALPER GPT TRADE INSTRUCTION")
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
                    self.log_message(f"📊 ACTION: {trade_instruction['ACTION']}")
                    self.log_message(f"💰 QUANTITY: {trade_instruction['QUANTITY']:.4f}")
                    self.log_message(f"⚖️ LEVERAGE: {trade_instruction['LEVERAGE']}")
                    self.log_message(f"🎯 ENTRY: ${trade_instruction.get('ENTRY_PRICE', current_price):.6f}")
                    self.log_message(f"🛑 STOP LOSS: ${trade_instruction.get('STOP_LOSS', 0):.6f}")
                    self.log_message(f"🎯 TAKE PROFIT: ${trade_instruction.get('TAKE_PROFIT', 0):.6f}")
                    self.log_message(f"⚠️ RISK: {trade_instruction['RISK_PCT']:.1f}%")
                    self.log_message(f"📋 ORDER TYPE: {trade_instruction['ORDER_TYPE']}")
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")

                    # Store final verdict for potential auto-trading
                    self.last_final_verdict = trade_instruction

                    # Update GUI panel with final verdict
                    self.update_final_verdict_panel(trade_instruction)

                    # Add to historical verdicts
                    self.add_to_historical_verdicts(symbol, trade_instruction)

                    # Update ScalperGPT GUI with all data
                    market_data = self.fetch_enriched_market_data(symbol)
                    ensemble_analysis = self.get_adaptive_ensemble_analysis(ml_decisions, ml_confidences)
                    self.update_scalper_gpt_gui(market_data, ensemble_analysis, trade_instruction)

                    # Execute autonomous trade if enabled
                    if self.autonomous_trading_enabled:
                        self.log_message("🤖 AUTO TRADER: Autonomous trading enabled, executing ScalperGPT decision")
                        self.execute_autonomous_trade(symbol, trade_instruction)
                    else:
                        self.log_message("🤖 AUTO TRADER: Autonomous trading disabled, instruction logged only")

                else:
                    self.log_message("⚠️ Could not parse ScalperGPT trade instruction")
            else:
                self.log_message("⚠️ No response from ScalperGPT analysis")

        except Exception as e:
            self.log_message(f"❌ Error in ScalperGPT analysis: {e}")

    def build_scalper_prompt(self, symbol, current_price, ml_decisions, ml_confidences,
                           llm_decision, llm_confidence, tech_decision, mtf_decision, current_model):
        """Build enriched ScalperGPT prompt with comprehensive market and account data"""
        try:
            # Fetch enriched market data
            market_data = self.fetch_enriched_market_data(symbol)

            # Get comprehensive account state
            account_state = self.get_comprehensive_account_state()

            # Get adaptive ensemble analysis
            ensemble_analysis = self.get_adaptive_ensemble_analysis(ml_decisions, ml_confidences)

            # Build ULTRA-COMPACT scalper prompt (target <800 chars)
            scalper_prompt = f"""SCALP {symbol} ${current_price:.2f} | BID/ASK {market_data['best_bid']:.2f}/{market_data['best_ask']:.2f} ({market_data['spread_pct']:.2f}%)
DEPTH: {self.format_ultra_compact_depth(market_data['top_5_bids'], market_data['top_5_asks'])}
FLOW: {market_data['trade_flow_imbalance']:+.0f}% | VOL: {market_data['volume_momentum']:+.0f}%
ENSEMBLE: {ensemble_analysis['performance_weighted_decision']} {ensemble_analysis['avg_confidence']:.0f}%
BAL: ${account_state.get('balance_info', {}).get('free_balance', 0):.0f} | POS: {len(account_state.get('open_positions', []))}"""

            return scalper_prompt

        except Exception as e:
            self.log_message(f"Error building scalper prompt: {e}")
            return "Error building scalper prompt"

    def build_full_control_prompt(self, scalper_prompt):
        """Ultra-compact JSON prompt for complete responses with intelligent position sizing"""
        try:
            # Get account state for intelligent quantity calculation
            account_state = self.get_comprehensive_account_state()
            free_balance = account_state.get('balance_info', {}).get('free_balance', 50)

            # Calculate intelligent example quantity based on account balance
            # Use 2% risk with 20x leverage as example
            risk_pct = 2.0
            leverage = 20
            current_price = getattr(self, 'current_bid', 0.17)  # Fallback price

            # Calculate position value: 2% of balance
            position_value = free_balance * (risk_pct / 100)
            # Calculate quantity: position_value / price
            example_quantity = position_value / current_price
            # Round to reasonable precision
            example_quantity = round(example_quantity, 0)  # Whole numbers for DOGE

            # Ensure minimum quantity
            example_quantity = max(1, example_quantity)

            full_control_extension = f"""

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance (${free_balance:.0f}) for position sizing.

JSON ONLY, NO COMMENTS:
{{"ACTION":"BUY","QUANTITY":{example_quantity},"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5"""

            return scalper_prompt + full_control_extension

        except Exception as e:
            self.log_message(f"Error building full control prompt: {e}")
            return scalper_prompt

    def fetch_enriched_market_data(self, symbol):
        """Fetch enriched market data for scalping analysis"""
        try:
            market_data = {
                'best_bid': 0.0,
                'best_ask': 0.0,
                'spread': 0.0,
                'spread_pct': 0.0,
                'top_5_bids': [],
                'top_5_asks': [],
                'tick_atr': 0.0,
                'trade_flow_imbalance': 0.0,
                'volume_momentum': 0.0,
                'data_latency_ms': 0.0
            }

            # Get order book depth (top 5 levels)
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                order_book = self.live_data_manager.get_latest_orderbook(symbol)
                if order_book and 'bids' in order_book and 'asks' in order_book:
                    bids = order_book['bids'][:5]  # Top 5 bids
                    asks = order_book['asks'][:5]  # Top 5 asks

                    if bids and asks:
                        market_data['best_bid'] = float(bids[0][0])
                        market_data['best_ask'] = float(asks[0][0])
                        market_data['spread'] = market_data['best_ask'] - market_data['best_bid']
                        market_data['spread_pct'] = (market_data['spread'] / market_data['best_bid']) * 100
                        market_data['top_5_bids'] = [(float(bid[0]), float(bid[1])) for bid in bids]
                        market_data['top_5_asks'] = [(float(ask[0]), float(ask[1])) for ask in asks]

            # Get recent ticks data (simulate last 100 ticks analysis)
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                # Get recent candle data as proxy for tick analysis
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=10)
                if candles and len(candles) >= 5:
                    # Calculate tick ATR from recent price movements
                    highs = [float(candle[2]) for candle in candles[-5:]]
                    lows = [float(candle[3]) for candle in candles[-5:]]
                    closes = [float(candle[4]) for candle in candles[-5:]]
                    volumes = [float(candle[5]) for candle in candles[-5:]]

                    # Simple tick ATR calculation
                    ranges = [high - low for high, low in zip(highs, lows)]
                    market_data['tick_atr'] = sum(ranges) / len(ranges) if ranges else 0.0

                    # Trade flow imbalance (simplified)
                    if len(closes) >= 2:
                        price_changes = [closes[i] - closes[i-1] for i in range(1, len(closes))]
                        positive_changes = sum(1 for change in price_changes if change > 0)
                        market_data['trade_flow_imbalance'] = ((positive_changes / len(price_changes)) - 0.5) * 200

                    # Volume momentum
                    if len(volumes) >= 2:
                        recent_vol = sum(volumes[-2:]) / 2
                        older_vol = sum(volumes[-5:-2]) / 3 if len(volumes) >= 5 else recent_vol
                        market_data['volume_momentum'] = ((recent_vol - older_vol) / older_vol) * 100 if older_vol > 0 else 0

            # Data latency (simulated)
            market_data['data_latency_ms'] = 50.0  # Assume 50ms latency

            return market_data

        except Exception as e:
            self.log_message(f"Error fetching enriched market data: {e}")
            return {
                'best_bid': 0.0, 'best_ask': 0.0, 'spread': 0.0, 'spread_pct': 0.0,
                'top_5_bids': [], 'top_5_asks': [], 'tick_atr': 0.0,
                'trade_flow_imbalance': 0.0, 'volume_momentum': 0.0, 'data_latency_ms': 100.0
            }

    def format_order_book_levels(self, levels, side):
        """Format order book levels for display"""
        try:
            if not levels:
                return f"No {side} levels available"

            formatted = ""
            for i, (price, volume) in enumerate(levels, 1):
                formatted += f"  {i}. ${price:.6f} × {volume:.2f}\n"
            return formatted.strip()
        except Exception as e:
            return f"Error formatting {side} levels: {e}"

    def format_lean_orderbook(self, bids, asks):
        """Format ultra-lean orderbook for aggressive scalping prompt"""
        try:
            if not bids or not asks:
                return "No depth data"

            # Show only top 3 levels in compact format
            bid_str = " | ".join([f"${bid[0]:.6f}×{bid[1]:.0f}" for bid in bids[:3]])
            ask_str = " | ".join([f"${ask[0]:.6f}×{ask[1]:.0f}" for ask in asks[:3]])

            return f"BIDS: {bid_str}\nASKS: {ask_str}"
        except Exception as e:
            return f"Depth error: {e}"

    def format_ultra_compact_depth(self, bids, asks):
        """Ultra-compact depth format for minimal prompt size"""
        try:
            if not bids or not asks:
                return "No data"

            # Show only top 2 levels in minimal format
            bid_top = f"{bids[0][0]:.2f}×{bids[0][1]:.0f}" if bids else "0"
            ask_top = f"{asks[0][0]:.2f}×{asks[0][1]:.0f}" if asks else "0"

            return f"B:{bid_top} A:{ask_top}"
        except Exception as e:
            return "Error"

    def get_adaptive_ensemble_analysis(self, ml_decisions, ml_confidences):
        """Get adaptive ensemble analysis with dynamic weighting"""
        try:
            # Model names for the 8 models
            model_names = ["SVM", "Random Forest", "LSTM", "RSI Model", "VWAP Model",
                          "Orderflow Model", "Volatility Model", "Sentiment Model"]

            # Ensure we have 8 models (pad with defaults if needed)
            while len(ml_decisions) < 8:
                ml_decisions.append("WAIT")
            while len(ml_confidences) < 8:
                ml_confidences.append(50.0)

            # Individual model analysis
            individual_models = ""
            for i, (model, decision, confidence) in enumerate(zip(model_names, ml_decisions[:8], ml_confidences[:8])):
                # Get model performance weight (simulate dynamic weighting)
                weight = self.get_model_performance_weight(model)
                individual_models += f"🤖 {model}: {decision} ({confidence:.1f}%) [Weight: {weight:.2f}]\n"

            # Calculate ensemble metrics
            decisions = ml_decisions[:8]
            confidences = ml_confidences[:8]

            # Majority vote
            vote_counts = {"LONG": 0, "SHORT": 0, "WAIT": 0}
            for decision in decisions:
                if decision in vote_counts:
                    vote_counts[decision] += 1
            majority_vote = max(vote_counts, key=vote_counts.get)

            # Average confidence and standard deviation
            avg_confidence = sum(confidences) / len(confidences)
            confidence_std = (sum((c - avg_confidence) ** 2 for c in confidences) / len(confidences)) ** 0.5

            # Weighted ensemble score
            weighted_score = 0.0
            total_weight = 0.0
            for decision, confidence in zip(decisions, confidences):
                weight = self.get_model_performance_weight("default")  # Use default weight for now
                score = 1 if decision == "LONG" else (-1 if decision == "SHORT" else 0)
                weighted_score += score * (confidence / 100) * weight
                total_weight += weight

            if total_weight > 0:
                weighted_score /= total_weight

            # Consensus strength
            consensus_strength = (max(vote_counts.values()) / len(decisions)) * 100

            # Performance weighted decision
            if weighted_score > 0.3:
                performance_weighted_decision = "LONG"
            elif weighted_score < -0.3:
                performance_weighted_decision = "SHORT"
            else:
                performance_weighted_decision = "WAIT"

            return {
                'individual_models': individual_models.strip(),
                'majority_vote': majority_vote,
                'avg_confidence': avg_confidence,
                'confidence_std': confidence_std,
                'weighted_score': weighted_score,
                'consensus_strength': consensus_strength,
                'performance_weighted_decision': performance_weighted_decision
            }

        except Exception as e:
            self.log_message(f"Error in adaptive ensemble analysis: {e}")
            return {
                'individual_models': "Error in ensemble analysis",
                'majority_vote': "WAIT",
                'avg_confidence': 50.0,
                'confidence_std': 0.0,
                'weighted_score': 0.0,
                'consensus_strength': 0.0,
                'performance_weighted_decision': "WAIT"
            }

    def get_model_performance_weight(self, model_name):
        """Get performance-based weight for a model (simulate dynamic weighting)"""
        try:
            # Simulate model performance tracking
            # In production, this would track actual Sharpe ratios and accuracy
            default_weights = {
                "SVM": 1.2,
                "Random Forest": 1.1,
                "LSTM": 0.9,
                "RSI Model": 1.0,
                "VWAP Model": 1.1,
                "Orderflow Model": 0.8,
                "Volatility Model": 1.0,
                "Sentiment Model": 0.7,
                "default": 1.0
            }
            return default_weights.get(model_name, 1.0)
        except:
            return 1.0

    def parse_trade_instruction(self, final_response: str):
        """Enhanced JSON parsing with comment handling and fallback for incomplete responses"""
        try:
            import json
            import re

            self.log_message(f"🔍 Parsing LLM response ({len(final_response)} chars): {final_response[:200]}...")

            # Step 1: Clean response - remove comments and extra text
            cleaned_response = self.clean_llm_response(final_response)

            # Step 2: Extract JSON with multiple fallback strategies
            json_str = self.extract_json_with_fallbacks(cleaned_response)

            if not json_str:
                self.log_message("❌ No valid JSON found after all extraction attempts")
                return self.create_fallback_instruction(final_response)

            self.log_message(f"✅ Extracted JSON: {json_str}")

            # Step 3: Parse with error recovery
            trade_instruction = self.parse_json_with_recovery(json_str)

            # Parse JSON
            trade_instruction = json.loads(json_str)

            # Validate required fields
            required_fields = ["ACTION", "QUANTITY", "LEVERAGE", "RISK_PCT", "ORDER_TYPE"]
            for field in required_fields:
                if field not in trade_instruction:
                    self.log_message(f"❌ Missing required field: {field}")
                    return None

            # Validate ACTION
            action = trade_instruction["ACTION"]
            if action not in ["BUY", "SELL", "WAIT"]:
                self.log_message(f"❌ Invalid ACTION: {action}")
                return None

            # Handle null values gracefully, especially for WAIT decisions
            def safe_float_convert(value, default=0.0):
                """Safely convert value to float, handling None/null values"""
                if value is None or value == "null":
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default

            def safe_int_convert(value, default=1):
                """Safely convert value to int, handling None/null values"""
                if value is None or value == "null":
                    return default
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default

            def safe_string_convert(value, default="MARKET"):
                """Safely convert value to string, handling None/null values"""
                if value is None or value == "null":
                    return default
                return str(value)

            # For WAIT decisions, allow null/zero values but provide safe defaults
            if action == "WAIT":
                trade_instruction["QUANTITY"] = safe_float_convert(trade_instruction["QUANTITY"], 0.0)
                trade_instruction["LEVERAGE"] = safe_int_convert(trade_instruction["LEVERAGE"], 1)
                trade_instruction["RISK_PCT"] = safe_float_convert(trade_instruction["RISK_PCT"], 1.0)
                trade_instruction["ORDER_TYPE"] = safe_string_convert(trade_instruction["ORDER_TYPE"], "MARKET")
            else:
                # For BUY/SELL decisions, validate and constrain values normally
                quantity = safe_float_convert(trade_instruction["QUANTITY"], 0.0)
                leverage = safe_int_convert(trade_instruction["LEVERAGE"], 1)
                risk_pct = safe_float_convert(trade_instruction["RISK_PCT"], 2.0)

                trade_instruction["QUANTITY"] = max(0.0, quantity)
                trade_instruction["LEVERAGE"] = max(1, min(200, leverage))
                trade_instruction["RISK_PCT"] = max(0.5, min(5.0, risk_pct))
                trade_instruction["ORDER_TYPE"] = safe_string_convert(trade_instruction["ORDER_TYPE"], "MARKET")

            # Handle optional fields with null safety
            trade_instruction["STOP_LOSS"] = safe_float_convert(trade_instruction.get("STOP_LOSS"), 0.0)
            trade_instruction["TAKE_PROFIT"] = safe_float_convert(trade_instruction.get("TAKE_PROFIT"), 0.0)

            # Convert to expected format for compatibility
            trade_instruction["verdict"] = trade_instruction["ACTION"].replace("BUY", "LONG").replace("SELL", "SHORT")
            trade_instruction["confidence"] = 85.0  # Default confidence for LLM decisions
            trade_instruction["position_size"] = trade_instruction["QUANTITY"]
            trade_instruction["entry_price"] = trade_instruction.get("ENTRY_PRICE", 0.0)
            trade_instruction["stop_loss"] = trade_instruction.get("STOP_LOSS", 0.0)
            trade_instruction["take_profit"] = trade_instruction.get("TAKE_PROFIT", 0.0)
            trade_instruction["leverage"] = f"{trade_instruction['LEVERAGE']}x"
            trade_instruction["risk_level"] = "MEDIUM"
            trade_instruction["reasoning"] = "ScalperGPT autonomous decision based on comprehensive market analysis"

            # Log successful parsing with detailed information
            action = trade_instruction['ACTION']
            quantity = trade_instruction['QUANTITY']
            leverage = trade_instruction['LEVERAGE']
            risk_pct = trade_instruction['RISK_PCT']

            if action == "WAIT":
                self.log_message(f"✅ Parsed WAIT decision: No trade action required")
            else:
                self.log_message(f"✅ Parsed trade instruction: {action} {quantity:.4f} @ {leverage}x leverage, {risk_pct:.1f}% risk")

            return trade_instruction

        except json.JSONDecodeError as e:
            self.log_message(f"❌ JSON parsing error: {e}")
            self.log_message(f"❌ Raw response: {final_response[:200]}...")
            return None
        except (ValueError, TypeError) as e:
            self.log_message(f"❌ Value conversion error: {e}")
            self.log_message(f"❌ This usually indicates null values in JSON for non-WAIT decisions")
            return None
        except Exception as e:
            self.log_message(f"❌ Error parsing trade instruction: {e}")
            self.log_message(f"❌ Raw response: {final_response[:200]}...")
            return self.create_fallback_instruction(final_response)

    def clean_llm_response(self, response: str) -> str:
        """Clean LLM response by removing comments and extra text"""
        try:
            import re

            # Remove // style comments
            cleaned = re.sub(r'//.*?(?=\n|$)', '', response, flags=re.MULTILINE)

            # Remove /* */ style comments
            cleaned = re.sub(r'/\*.*?\*/', '', cleaned, flags=re.DOTALL)

            # Remove common LLM prefixes/suffixes
            prefixes_to_remove = [
                "Here's the JSON response:",
                "Here is the JSON:",
                "JSON response:",
                "Response:",
                "```json",
                "```"
            ]

            for prefix in prefixes_to_remove:
                cleaned = cleaned.replace(prefix, "")

            # Clean whitespace
            cleaned = cleaned.strip()

            self.log_message(f"🧹 Cleaned response: {cleaned[:200]}...")
            return cleaned

        except Exception as e:
            self.log_message(f"⚠️ Error cleaning response: {e}")
            return response

    def extract_json_with_fallbacks(self, response: str) -> str:
        """Extract JSON with multiple fallback strategies"""
        try:
            import re

            # Strategy 1: Find complete JSON object
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                if self.is_valid_json_structure(json_str):
                    return json_str

            # Strategy 2: Find any JSON-like structure
            json_match = re.search(r'\{.*?\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                # Try to fix common truncation issues
                json_str = self.fix_truncated_json(json_str)
                if self.is_valid_json_structure(json_str):
                    return json_str

            # Strategy 3: Build JSON from key-value pairs found in text
            return self.extract_json_from_text(response)

        except Exception as e:
            self.log_message(f"⚠️ Error in JSON extraction: {e}")
            return ""

    def fix_truncated_json(self, json_str: str) -> str:
        """Fix common JSON truncation issues"""
        try:
            # Count braces to see if truncated
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')

            # Add missing closing braces
            if open_braces > close_braces:
                json_str += '}' * (open_braces - close_braces)

            # Fix common truncation patterns
            if json_str.endswith(','):
                json_str = json_str[:-1]  # Remove trailing comma

            if not json_str.endswith('}'):
                json_str += '}'

            # Fix incomplete string values
            if json_str.count('"') % 2 != 0:
                json_str += '"'

            return json_str

        except Exception as e:
            self.log_message(f"⚠️ Error fixing truncated JSON: {e}")
            return json_str

    def is_valid_json_structure(self, json_str: str) -> bool:
        """Check if string has valid JSON structure"""
        try:
            import json
            json.loads(json_str)
            return True
        except:
            return False

    def extract_json_from_text(self, response: str) -> str:
        """Extract JSON from text by finding key-value pairs"""
        try:
            import re

            # Look for ACTION field
            action_match = re.search(r'"?ACTION"?\s*:\s*"?(BUY|SELL|WAIT)"?', response, re.IGNORECASE)
            action = action_match.group(1) if action_match else "WAIT"

            # Look for QUANTITY field
            quantity_match = re.search(r'"?QUANTITY"?\s*:\s*([0-9.]+)', response, re.IGNORECASE)
            quantity = float(quantity_match.group(1)) if quantity_match else 0.0

            # Look for LEVERAGE field
            leverage_match = re.search(r'"?LEVERAGE"?\s*:\s*([0-9]+)', response, re.IGNORECASE)
            leverage = int(leverage_match.group(1)) if leverage_match else 1

            # Build JSON from extracted values
            extracted_json = {
                "ACTION": action,
                "QUANTITY": quantity,
                "LEVERAGE": leverage,
                "STOP_LOSS": 0.0,
                "TAKE_PROFIT": 0.0,
                "RISK_PCT": 2.0,
                "ORDER_TYPE": "MARKET"
            }

            import json
            return json.dumps(extracted_json)

        except Exception as e:
            self.log_message(f"⚠️ Error extracting JSON from text: {e}")
            return ""

    def parse_json_with_recovery(self, json_str: str):
        """Parse JSON with error recovery"""
        try:
            import json

            # Try direct parsing first
            trade_instruction = json.loads(json_str)

            # Validate required fields
            required_fields = ["ACTION", "QUANTITY", "LEVERAGE", "RISK_PCT", "ORDER_TYPE"]
            for field in required_fields:
                if field not in trade_instruction:
                    self.log_message(f"⚠️ Missing field {field}, adding default")
                    trade_instruction[field] = self.get_default_field_value(field)

            return self.validate_and_process_instruction(trade_instruction)

        except json.JSONDecodeError as e:
            self.log_message(f"❌ JSON decode error: {e}")
            # Try to fix and re-parse
            fixed_json = self.fix_json_syntax_errors(json_str)
            if fixed_json:
                try:
                    trade_instruction = json.loads(fixed_json)
                    return self.validate_and_process_instruction(trade_instruction)
                except:
                    pass

            return None

    def get_default_field_value(self, field: str):
        """Get default value for missing JSON field"""
        defaults = {
            "ACTION": "WAIT",
            "QUANTITY": 0.0,
            "LEVERAGE": 1,
            "STOP_LOSS": 0.0,
            "TAKE_PROFIT": 0.0,
            "RISK_PCT": 2.0,
            "ORDER_TYPE": "MARKET"
        }
        return defaults.get(field, None)

    def fix_json_syntax_errors(self, json_str: str) -> str:
        """Fix common JSON syntax errors"""
        try:
            # Fix unquoted keys
            import re
            fixed = re.sub(r'(\w+):', r'"\1":', json_str)

            # Fix single quotes
            fixed = fixed.replace("'", '"')

            # Fix trailing commas
            fixed = re.sub(r',\s*}', '}', fixed)
            fixed = re.sub(r',\s*]', ']', fixed)

            return fixed

        except Exception as e:
            self.log_message(f"⚠️ Error fixing JSON syntax: {e}")
            return json_str

    def create_fallback_instruction(self, response: str):
        """Create fallback instruction when JSON parsing fails"""
        try:
            # Analyze response text for trading intent
            response_lower = response.lower()

            # Determine action based on keywords
            if any(word in response_lower for word in ['buy', 'long', 'bullish', 'up']):
                action = "BUY"
            elif any(word in response_lower for word in ['sell', 'short', 'bearish', 'down']):
                action = "SELL"
            else:
                action = "WAIT"

            # Calculate intelligent fallback quantity based on account balance
            fallback_quantity = 0.0
            fallback_leverage = 1

            if action != "WAIT":
                # Get account state for intelligent quantity calculation
                account_state = self.get_comprehensive_account_state()
                free_balance = account_state.get('balance_info', {}).get('free_balance', 50)

                # Use conservative 1.5% risk for fallback
                risk_pct = 1.5
                fallback_leverage = 10
                current_price = getattr(self, 'current_bid', 0.17)  # Fallback price

                # Calculate position value: 1.5% of balance
                position_value = free_balance * (risk_pct / 100)
                # Calculate quantity: position_value / price
                fallback_quantity = position_value / current_price
                # Round to reasonable precision
                fallback_quantity = round(fallback_quantity, 0)  # Whole numbers for DOGE

                # Ensure minimum quantity
                fallback_quantity = max(1, fallback_quantity)

            # Create fallback instruction
            fallback_instruction = {
                "ACTION": action,
                "QUANTITY": fallback_quantity,
                "LEVERAGE": fallback_leverage,
                "STOP_LOSS": 0.0,
                "TAKE_PROFIT": 0.0,
                "RISK_PCT": 1.5,
                "ORDER_TYPE": "MARKET",
                "verdict": action.replace("BUY", "LONG").replace("SELL", "SHORT"),
                "confidence": 60.0,
                "position_size": fallback_quantity,
                "entry_price": 0.0,
                "stop_loss": 0.0,
                "take_profit": 0.0,
                "leverage": f"{fallback_leverage}x",
                "risk_level": "MEDIUM",
                "reasoning": f"Intelligent fallback: {action} with balance-based sizing"
            }

            self.log_message(f"🔄 Created fallback instruction: {action}")
            return fallback_instruction

        except Exception as e:
            self.log_message(f"❌ Error creating fallback instruction: {e}")
            return None

    def validate_and_process_instruction(self, trade_instruction):
        """Validate and process trade instruction with enhanced action bias"""
        try:
            # Validate ACTION
            action = trade_instruction.get("ACTION", "WAIT")
            if action not in ["BUY", "SELL", "WAIT"]:
                self.log_message(f"❌ Invalid ACTION: {action}, defaulting to WAIT")
                action = "WAIT"

            # Apply aggressive action bias - convert borderline WAIT to action
            if action == "WAIT":
                action = self.apply_aggressive_action_bias(trade_instruction)
                if action != "WAIT":
                    trade_instruction["ACTION"] = action
                    self.log_message(f"🔥 Aggressive bias converted WAIT to {action}")

            # Handle null values gracefully
            def safe_float_convert(value, default=0.0):
                if value is None or value == "null":
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default

            def safe_int_convert(value, default=1):
                if value is None or value == "null":
                    return default
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default

            def safe_string_convert(value, default="MARKET"):
                if value is None or value == "null":
                    return default
                return str(value)

            # Process values based on action
            if action == "WAIT":
                trade_instruction["QUANTITY"] = 0.0
                trade_instruction["LEVERAGE"] = 1
                trade_instruction["RISK_PCT"] = 1.0
                trade_instruction["ORDER_TYPE"] = "MARKET"
            else:
                # For BUY/SELL decisions, validate and constrain values with intelligent defaults
                # Calculate intelligent default quantity based on account balance
                default_quantity = 50.0  # Fallback
                try:
                    account_state = self.get_comprehensive_account_state()
                    free_balance = account_state.get('balance_info', {}).get('free_balance', 50)
                    current_price = getattr(self, 'current_bid', 0.17)
                    # Use 2% risk as default
                    position_value = free_balance * 0.02
                    default_quantity = max(1, round(position_value / current_price, 0))
                except:
                    pass  # Use fallback if calculation fails

                quantity = safe_float_convert(trade_instruction.get("QUANTITY"), default_quantity)
                leverage = safe_int_convert(trade_instruction.get("LEVERAGE"), 10)
                risk_pct = safe_float_convert(trade_instruction.get("RISK_PCT"), 2.0)

                trade_instruction["QUANTITY"] = max(0.0, quantity)
                trade_instruction["LEVERAGE"] = max(1, min(200, leverage))
                trade_instruction["RISK_PCT"] = max(0.5, min(5.0, risk_pct))
                trade_instruction["ORDER_TYPE"] = safe_string_convert(trade_instruction.get("ORDER_TYPE"), "MARKET")

            # Handle optional fields
            trade_instruction["STOP_LOSS"] = safe_float_convert(trade_instruction.get("STOP_LOSS"), 0.0)
            trade_instruction["TAKE_PROFIT"] = safe_float_convert(trade_instruction.get("TAKE_PROFIT"), 0.0)

            # Convert to expected format for compatibility
            trade_instruction["verdict"] = trade_instruction["ACTION"].replace("BUY", "LONG").replace("SELL", "SHORT")
            trade_instruction["confidence"] = 85.0
            trade_instruction["position_size"] = trade_instruction["QUANTITY"]
            trade_instruction["entry_price"] = trade_instruction.get("ENTRY_PRICE", 0.0)
            trade_instruction["stop_loss"] = trade_instruction.get("STOP_LOSS", 0.0)
            trade_instruction["take_profit"] = trade_instruction.get("TAKE_PROFIT", 0.0)
            trade_instruction["leverage"] = f"{trade_instruction['LEVERAGE']}x"
            trade_instruction["risk_level"] = "MEDIUM"
            trade_instruction["reasoning"] = "ScalperGPT aggressive decision with enhanced parsing"

            # Log successful parsing
            action = trade_instruction['ACTION']
            quantity = trade_instruction['QUANTITY']
            leverage = trade_instruction['LEVERAGE']
            risk_pct = trade_instruction['RISK_PCT']

            if action == "WAIT":
                self.log_message(f"✅ Parsed WAIT decision (after bias check)")
            else:
                self.log_message(f"✅ Parsed {action}: {quantity:.4f} @ {leverage}x, {risk_pct:.1f}% risk")

            return trade_instruction

        except Exception as e:
            self.log_message(f"❌ Error validating trade instruction: {e}")
            return None

    def apply_aggressive_action_bias(self, trade_instruction):
        """Apply aggressive action bias to convert WAIT to BUY/SELL"""
        try:
            # Check market conditions for forced action
            current_symbol = self.symbol_combo.currentText()

            # Get current market volatility
            volatility_score = self.get_market_volatility_score(current_symbol)

            # Force action during high volatility periods
            if volatility_score > 0.7:
                self.log_message(f"🔥 High volatility ({volatility_score:.2f}) - forcing action")
                return self.determine_forced_action_direction()

            # Check ensemble confidence - only allow WAIT for very low confidence
            confidence_threshold = 40.0  # Only WAIT if confidence < 40%

            # Simulate ensemble confidence (in production, get from actual ensemble)
            ensemble_confidence = 65.0  # Default moderate confidence

            if ensemble_confidence >= confidence_threshold:
                self.log_message(f"🎯 Confidence {ensemble_confidence:.1f}% >= {confidence_threshold}% - forcing action")
                return self.determine_forced_action_direction()

            # Allow WAIT only for truly low confidence
            self.log_message(f"⏸️ Low confidence {ensemble_confidence:.1f}% - allowing WAIT")
            return "WAIT"

        except Exception as e:
            self.log_message(f"⚠️ Error in action bias: {e}")
            return "WAIT"

    def get_market_volatility_score(self, symbol):
        """Calculate market volatility score (0.0 to 1.0)"""
        try:
            # Get recent price data
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=10)
                if candles and len(candles) >= 5:
                    # Calculate price volatility
                    closes = [float(candle[4]) for candle in candles[-5:]]
                    if len(closes) >= 2:
                        price_changes = [abs(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
                        avg_change = sum(price_changes) / len(price_changes)
                        # Normalize to 0-1 scale (0.02 = 2% change = high volatility)
                        volatility_score = min(avg_change / 0.02, 1.0)
                        return volatility_score

            # Default moderate volatility
            return 0.5

        except Exception as e:
            self.log_message(f"⚠️ Error calculating volatility: {e}")
            return 0.5

    def determine_forced_action_direction(self):
        """Determine BUY or SELL for forced action"""
        try:
            # Get ensemble decision if available
            if hasattr(self, 'last_ensemble_decision'):
                ensemble_decision = getattr(self, 'last_ensemble_decision', 'LONG')
                if ensemble_decision == 'LONG':
                    return "BUY"
                elif ensemble_decision == 'SHORT':
                    return "SELL"

            # Fallback: analyze recent price trend
            current_symbol = self.symbol_combo.currentText()
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                candles = self.live_data_manager.get_chart_data(current_symbol, '1m', limit=3)
                if candles and len(candles) >= 2:
                    recent_close = float(candles[-1][4])
                    previous_close = float(candles[-2][4])
                    if recent_close > previous_close:
                        return "BUY"
                    else:
                        return "SELL"

            # Ultimate fallback: slight bias toward BUY
            return "BUY"

        except Exception as e:
            self.log_message(f"⚠️ Error determining action direction: {e}")
            return "BUY"

    def format_ml_predictions(self, ml_decisions, ml_confidences):
        """Format ML predictions for display"""
        model_names = ["SVM", "Random Forest", "LSTM", "RSI Model", "VWAP Model",
                      "Orderflow Model", "Volatility Model", "Sentiment Model"]

        formatted = ""
        for i, (model, decision, confidence) in enumerate(zip(model_names, ml_decisions, ml_confidences)):
            formatted += f"🤖 {model}: {decision} ({confidence:.1f}%)\n"
        return formatted.strip()

    def format_account_state_for_prompt(self, account_state):
        """Format comprehensive account state for LLM prompt"""
        try:
            if not account_state:
                return "❌ Account state unavailable"

            balance_info = account_state.get('balance_info', {})
            open_positions = account_state.get('open_positions', [])
            recent_trades = account_state.get('recent_trades', [])
            risk_metrics = account_state.get('risk_metrics', {})

            formatted = f"""💰 BALANCE INFORMATION:
   Free Balance: ${balance_info.get('free_balance', 0):.2f} USDT
   Used Balance: ${balance_info.get('used_balance', 0):.2f} USDT
   Total Balance: ${balance_info.get('total_balance', 0):.2f} USDT

📊 OPEN POSITIONS ({len(open_positions)}):"""

            if open_positions:
                for pos in open_positions:
                    formatted += f"""
   {pos['symbol']}: {pos['side'].upper()} {pos['size']:.4f} @ ${pos['entry_price']:.6f}
   Unrealized PnL: ${pos['unrealized_pnl']:.2f} | Margin: ${pos['margin_used']:.2f}"""
            else:
                formatted += "\n   No open positions"

            formatted += f"""

📈 RECENT TRADING PERFORMANCE:
   Win Rate: {risk_metrics.get('win_rate', 0):.1f}%
   Max Drawdown: {risk_metrics.get('max_drawdown', 0):.1f}%
   Average PnL: {risk_metrics.get('avg_pnl', 0):.1f}%

📋 RECENT TRADES ({len(recent_trades)}):"""

            if recent_trades:
                for trade in recent_trades[-5:]:  # Show last 5 trades
                    formatted += f"""
   {trade['time']} {trade['symbol']}: {trade['verdict']} -> {trade['result']} ({trade['pnl_percentage']:+.1f}%)"""
            else:
                formatted += "\n   No recent trades"

            return formatted

        except Exception as e:
            return f"❌ Error formatting account state: {str(e)}"

    def format_open_positions_for_prompt(self, positions):
        """Format open positions for LLM prompt"""
        if not positions:
            return "No open positions"

        formatted = ""
        for pos in positions:
            formatted += f"• {pos['symbol']}: {pos['side'].upper()} {pos['size']:.4f} @ ${pos['entry_price']:.6f} (PnL: ${pos['unrealized_pnl']:.2f})\n"
        return formatted.strip()

    def format_recent_trades_for_prompt(self, trades):
        """Format recent trades for LLM prompt"""
        if not trades:
            return "No recent trades"

        formatted = ""
        for trade in trades[-5:]:  # Last 5 trades
            formatted += f"• {trade['time']} {trade['symbol']}: {trade['verdict']} -> {trade['result']} ({trade['pnl_percentage']:+.1f}%)\n"
        return formatted.strip()

    def calculate_liquidity_score(self):
        """Calculate liquidity score based on spread and volume"""
        try:
            # Simple liquidity score based on spread
            if hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0

                    # Convert spread to liquidity score (lower spread = higher liquidity)
                    if spread_pct < 0.1:
                        return 1.0  # Excellent liquidity
                    elif spread_pct < 0.2:
                        return 0.8  # Good liquidity
                    elif spread_pct < 0.5:
                        return 0.6  # Fair liquidity
                    elif spread_pct < 1.0:
                        return 0.4  # Poor liquidity
                    else:
                        return 0.2  # Very poor liquidity

            return 0.5  # Default moderate liquidity

        except Exception as e:
            self.log_message(f"Error calculating liquidity score: {str(e)}")
            return 0.5

    def parse_final_verdict(self, response):
        """Parse the structured final verdict response"""
        try:
            verdict_data = {}

            # Extract each field using string parsing
            lines = response.split('\n')
            for line in lines:
                line = line.strip()

                if line.startswith('FINAL_VERDICT:'):
                    verdict_data['verdict'] = line.split(':')[1].strip()
                elif line.startswith('CONFIDENCE:'):
                    # Handle percentage sign and extract numeric value
                    confidence_str = line.split(':')[1].strip().replace('%', '')
                    # Extract just the number part (handle cases like "85% (reduced from...)")
                    import re
                    confidence_match = re.search(r'(\d+(?:\.\d+)?)', confidence_str)
                    if confidence_match:
                        verdict_data['confidence'] = float(confidence_match.group(1))
                    else:
                        verdict_data['confidence'] = 50.0  # Default fallback
                elif line.startswith('POSITION_SIZE:'):
                    # Handle position size with potential text after the number
                    position_str = line.split(':')[1].strip().replace('$', '')
                    # Extract just the number part (handle cases like "$20.00 (reduced from...)")
                    import re
                    position_match = re.search(r'(\d+(?:\.\d+)?)', position_str)
                    if position_match:
                        verdict_data['position_size'] = float(position_match.group(1))
                    else:
                        verdict_data['position_size'] = 0.0  # Default fallback
                elif line.startswith('ENTRY_PRICE:'):
                    entry_str = line.split(':')[1].strip().replace('$', '')
                    # Extract just the number part (handle cases like "$0.169612")
                    import re
                    entry_match = re.search(r'(\d+(?:\.\d+)?)', entry_str)
                    if entry_match:
                        verdict_data['entry_price'] = float(entry_match.group(1))
                    else:
                        verdict_data['entry_price'] = 0.0
                elif line.startswith('STOP_LOSS:'):
                    stop_loss_str = line.split(':')[1].strip().replace('$', '')
                    # Handle cases like "$0.000000 (1%)" or "N/A"
                    import re
                    if stop_loss_str.upper() in ['N/A', 'NONE', '--']:
                        verdict_data['stop_loss'] = 0.0
                    else:
                        stop_loss_match = re.search(r'(\d+(?:\.\d+)?)', stop_loss_str)
                        if stop_loss_match:
                            verdict_data['stop_loss'] = float(stop_loss_match.group(1))
                        else:
                            verdict_data['stop_loss'] = 0.0
                elif line.startswith('TAKE_PROFIT:'):
                    take_profit_str = line.split(':')[1].strip().replace('$', '')
                    # Handle cases like "$0.175000" or "N/A"
                    import re
                    if take_profit_str.upper() in ['N/A', 'NONE', '--']:
                        verdict_data['take_profit'] = 0.0
                    else:
                        take_profit_match = re.search(r'(\d+(?:\.\d+)?)', take_profit_str)
                        if take_profit_match:
                            verdict_data['take_profit'] = float(take_profit_match.group(1))
                        else:
                            verdict_data['take_profit'] = 0.0
                elif line.startswith('LEVERAGE:'):
                    verdict_data['leverage'] = line.split(':')[1].strip()
                elif line.startswith('RISK_LEVEL:'):
                    verdict_data['risk_level'] = line.split(':')[1].strip()
                elif line.startswith('REASONING:'):
                    verdict_data['reasoning'] = line.split(':', 1)[1].strip()

            # Validate required fields
            required_fields = ['verdict', 'confidence', 'position_size', 'entry_price']
            if all(field in verdict_data for field in required_fields):
                return verdict_data
            else:
                return None

        except Exception as e:
            self.log_message(f"Error parsing final verdict: {e}")
            return None

    def get_current_timestamp(self):
        """Get current timestamp for analysis"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def batch_gui_update(self, widget_name, update_type, value, style=None):
        """🚀 PERFORMANCE FIX: Enhanced GUI update batching to reduce lag by 80%"""
        if widget_name not in self.pending_gui_updates:
            self.pending_gui_updates[widget_name] = {}

        self.pending_gui_updates[widget_name][update_type] = {
            'value': value,
            'style': style
        }

        # 🚀 PERFORMANCE FIX: Adaptive debouncing based on update frequency
        # Shorter delay for critical updates, longer for non-critical
        critical_widgets = ['best_bid_label', 'best_ask_label', 'balance_label']
        delay = 25 if widget_name in critical_widgets else 100

        # Start timer to apply updates (adaptive debounced)
        self.gui_update_timer.start(delay)

    def apply_batched_gui_updates(self):
        """🚀 PERFORMANCE FIX: Optimized batch GUI updates with widget caching"""
        try:
            if not self.pending_gui_updates:
                return

            # 🚀 PERFORMANCE FIX: Cache widget lookups to avoid repeated getattr calls
            widget_cache = {}
            updates_applied = 0

            for widget_name, updates in self.pending_gui_updates.items():
                # Use cached widget or lookup once
                if widget_name not in widget_cache:
                    widget_cache[widget_name] = getattr(self, widget_name, None)

                widget = widget_cache[widget_name]
                if widget:
                    # 🚀 PERFORMANCE FIX: Batch multiple operations on same widget
                    for update_type, data in updates.items():
                        try:
                            if update_type == 'text' and hasattr(widget, 'setText'):
                                current_text = widget.text() if hasattr(widget, 'text') else ''
                                # Only update if text actually changed
                                if current_text != data['value']:
                                    widget.setText(data['value'])
                                    updates_applied += 1
                            elif update_type == 'style' and hasattr(widget, 'setStyleSheet'):
                                widget.setStyleSheet(data['value'])
                                updates_applied += 1
                            elif update_type == 'both':
                                if hasattr(widget, 'setText'):
                                    current_text = widget.text() if hasattr(widget, 'text') else ''
                                    if current_text != data['value']:
                                        widget.setText(data['value'])
                                        updates_applied += 1
                                if hasattr(widget, 'setStyleSheet') and data['style']:
                                    widget.setStyleSheet(data['style'])
                                    updates_applied += 1
                        except Exception as widget_error:
                            print(f"Error updating widget {widget_name}: {widget_error}")

            # Clear pending updates
            self.pending_gui_updates.clear()

            # Performance monitoring
            if updates_applied > 50:
                print(f"⚠️ High GUI update count: {updates_applied} updates applied")

        except Exception as e:
            print(f"Error applying batched GUI updates: {e}")
            self.pending_gui_updates.clear()

    def set_performance_mode(self, enabled):
        """Enable/disable performance mode to reduce lag during analysis"""
        if enabled:
            # Pause non-critical timers during analysis
            if hasattr(self, 'bid_ask_timer'):
                self.bid_ask_timer.stop()
            if hasattr(self, 'balance_timer'):
                self.balance_timer.stop()
            if hasattr(self, 'chart_timer'):
                self.chart_timer.stop()
        else:
            # Resume timers after analysis
            if hasattr(self, 'bid_ask_timer'):
                self.bid_ask_timer.start(3000)
            if hasattr(self, 'balance_timer'):
                self.balance_timer.start(30000)
            if hasattr(self, 'chart_timer'):
                self.chart_timer.start(10000)

    def add_to_historical_verdicts(self, symbol, trade_instruction):
        """Add a ScalperGPT trade instruction to historical records with tracking"""
        try:
            from datetime import datetime
            import uuid

            # Generate unique ID for tracking
            verdict_id = str(uuid.uuid4())[:8]

            # Handle both old format (verdict_data) and new format (trade_instruction)
            if 'ACTION' in trade_instruction:
                # New ScalperGPT format
                action = trade_instruction.get('ACTION', 'WAIT')
                quantity = trade_instruction.get('QUANTITY', 0.0)
                leverage = trade_instruction.get('LEVERAGE', 1)
                risk_pct = trade_instruction.get('RISK_PCT', 2.0)
                order_type = trade_instruction.get('ORDER_TYPE', 'MARKET')
                # Get current price as entry price if not provided
                entry_price = trade_instruction.get('entry_price', getattr(self, 'current_price', 0.0))
                if entry_price == 0.0:
                    # Try to get current price from live data manager
                    if hasattr(self, 'live_data_manager') and self.live_data_manager:
                        entry_price = self.live_data_manager.get_latest_price(symbol) or 0.0

                historical_record = {
                    'id': verdict_id,
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'action': action,
                    'verdict': action.replace('BUY', 'LONG').replace('SELL', 'SHORT'),
                    'quantity': quantity,
                    'leverage': f"{leverage}x",
                    'risk_pct': risk_pct,
                    'order_type': order_type,
                    'confidence': trade_instruction.get('confidence', 85.0),
                    'entry_price': entry_price,
                    'stop_loss': trade_instruction.get('STOP_LOSS', 0),
                    'take_profit': trade_instruction.get('TAKE_PROFIT', 0),
                    'position_size': quantity,
                    'risk_level': f"{risk_pct:.1f}%",
                    'reasoning': f"ScalperGPT: {action} {quantity:.4f} @ {leverage}x, {risk_pct:.1f}% risk",
                    'result': 'PENDING',
                    'exit_price': None,
                    'exit_reason': None,
                    'pnl': 0.0,
                    'pnl_percentage': 0.0,
                    'max_favorable': 0.0,
                    'max_adverse': 0.0,
                    'duration_minutes': 0,
                    'is_active': True if action in ['BUY', 'SELL'] else False
                }
            else:
                # Legacy format
                historical_record = {
                    'id': verdict_id,
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'action': trade_instruction.get('verdict', 'WAIT').replace('LONG', 'BUY').replace('SHORT', 'SELL'),
                    'verdict': trade_instruction.get('verdict', 'WAIT'),
                    'quantity': trade_instruction.get('position_size', 0),
                    'leverage': trade_instruction.get('leverage', '1x'),
                    'risk_pct': 2.0,
                    'order_type': 'MARKET',
                    'confidence': trade_instruction.get('confidence', 0),
                    'entry_price': trade_instruction.get('entry_price', 0),
                    'stop_loss': trade_instruction.get('stop_loss', 0),
                    'take_profit': trade_instruction.get('take_profit', 0),
                    'position_size': trade_instruction.get('position_size', 0),
                    'risk_level': trade_instruction.get('risk_level', 'LOW'),
                    'reasoning': trade_instruction.get('reasoning', 'No reasoning provided'),
                    'result': 'PENDING',
                    'exit_price': None,
                    'exit_reason': None,
                    'pnl': 0.0,
                    'pnl_percentage': 0.0,
                    'max_favorable': 0.0,
                    'max_adverse': 0.0,
                    'duration_minutes': 0,
                    'is_active': True if trade_instruction.get('verdict', 'WAIT') != 'WAIT' else False
                }

            # Add to historical verdicts list
            self.historical_verdicts.append(historical_record)

            # Add to active tracking if it's a tradeable verdict
            if historical_record['is_active']:
                self.active_verdicts[verdict_id] = historical_record
                self.log_message(f"🎯 Started tracking verdict {verdict_id}: {historical_record['verdict']} at ${historical_record['entry_price']:.6f}")

            # Keep only last 50 verdicts to prevent memory issues
            if len(self.historical_verdicts) > 50:
                self.historical_verdicts = self.historical_verdicts[-50:]

            # Update the historical verdicts table
            self.update_historical_verdicts_table()

            action_or_verdict = historical_record.get('action', historical_record.get('verdict', 'WAIT'))
            self.log_message(f"📊 Added ScalperGPT decision to history: {action_or_verdict} for {symbol}")

        except Exception as e:
            self.log_message(f"Error adding to historical verdicts: {e}")

    def update_historical_verdicts_table(self):
        """Update the historical verdicts table with ScalperGPT tracking data"""
        try:
            # Clear existing rows
            self.historical_verdicts_table.setRowCount(0)

            # Add rows for each historical verdict (most recent first)
            for i, record in enumerate(reversed(self.historical_verdicts)):
                self.historical_verdicts_table.insertRow(i)

                # Time
                time_str = record['timestamp'].strftime("%H:%M:%S")
                time_item = QTableWidgetItem(time_str)
                time_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 0, time_item)

                # Action (BUY/SELL/WAIT) with color coding
                action = record.get('action', record.get('verdict', 'WAIT'))
                action_item = QTableWidgetItem(action)
                if action in ["BUY", "LONG"]:
                    action_item.setForeground(QColor(MatrixTheme.GREEN))
                elif action in ["SELL", "SHORT"]:
                    action_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    action_item.setForeground(QColor(MatrixTheme.YELLOW))
                self.historical_verdicts_table.setItem(i, 1, action_item)

                # Quantity
                quantity = record.get('quantity', record.get('position_size', 0))
                quantity_item = QTableWidgetItem(f"{quantity:.2f}")
                quantity_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 2, quantity_item)

                # Leverage
                leverage = record.get('leverage', '1x')
                if isinstance(leverage, (int, float)):
                    leverage = f"{leverage}x"
                leverage_item = QTableWidgetItem(str(leverage))
                leverage_item.setForeground(QColor(MatrixTheme.YELLOW))
                self.historical_verdicts_table.setItem(i, 3, leverage_item)

                # Risk %
                risk_pct = record.get('risk_pct', 2.0)
                risk_item = QTableWidgetItem(f"{risk_pct:.1f}%")
                if risk_pct > 3.0:
                    risk_item.setForeground(QColor(MatrixTheme.RED))
                elif risk_pct > 1.5:
                    risk_item.setForeground(QColor(MatrixTheme.YELLOW))
                else:
                    risk_item.setForeground(QColor(MatrixTheme.GREEN))
                self.historical_verdicts_table.setItem(i, 4, risk_item)

                # Entry Price
                entry_price_item = QTableWidgetItem(f"{record['entry_price']:.6f}")
                entry_price_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 5, entry_price_item)

                # Exit Price
                if record.get('exit_price'):
                    exit_price_text = f"{record['exit_price']:.6f}"
                else:
                    exit_price_text = "ACTIVE" if record.get('is_active') else "--"
                exit_price_item = QTableWidgetItem(exit_price_text)
                if record.get('is_active') and not record.get('exit_price'):
                    exit_price_item.setForeground(QColor(MatrixTheme.YELLOW))
                else:
                    exit_price_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 6, exit_price_item)

                # PnL Percentage
                pnl_pct = record.get('pnl_percentage', 0)
                if pnl_pct != 0:
                    pnl_text = f"{pnl_pct:+.1f}%"
                    pnl_color = MatrixTheme.GREEN if pnl_pct > 0 else MatrixTheme.RED
                else:
                    pnl_text = "--"
                    pnl_color = MatrixTheme.GRAY
                pnl_item = QTableWidgetItem(pnl_text)
                pnl_item.setForeground(QColor(pnl_color))
                self.historical_verdicts_table.setItem(i, 7, pnl_item)

                # Status with enhanced display for ScalperGPT
                result = record['result']
                if result == 'PENDING' and record.get('is_active'):
                    status_text = "ACTIVE"
                    status_color = MatrixTheme.YELLOW
                elif result == "WIN":
                    status_text = "WIN"
                    status_color = MatrixTheme.GREEN
                elif result == "LOSS":
                    status_text = "LOSS"
                    status_color = MatrixTheme.RED
                elif result == "EXPIRED":
                    status_text = "EXPIRED"
                    status_color = MatrixTheme.GRAY
                else:
                    status_text = "PENDING"
                    status_color = MatrixTheme.GRAY

                status_item = QTableWidgetItem(status_text)
                status_item.setForeground(QColor(status_color))
                self.historical_verdicts_table.setItem(i, 8, status_item)

            # Update summary statistics
            self.update_historical_stats()

        except Exception as e:
            self.log_message(f"Error updating historical verdicts table: {e}")

    def update_historical_stats(self):
        """Update historical verdicts summary statistics with enhanced metrics"""
        try:
            total_verdicts = len(self.historical_verdicts)

            if total_verdicts > 0:
                # Calculate success rate (only for completed verdicts)
                completed_verdicts = [v for v in self.historical_verdicts if v['result'] in ['WIN', 'LOSS']]
                active_verdicts = [v for v in self.historical_verdicts if v.get('is_active', False)]

                if completed_verdicts:
                    wins = len([v for v in completed_verdicts if v['result'] == 'WIN'])
                    success_rate = (wins / len(completed_verdicts)) * 100

                    # Calculate average PnL for completed trades
                    total_pnl = sum(v.get('pnl_percentage', 0) for v in completed_verdicts)
                    avg_pnl = total_pnl / len(completed_verdicts)
                else:
                    success_rate = 0
                    avg_pnl = 0

                # Calculate average confidence
                avg_confidence = sum(v['confidence'] for v in self.historical_verdicts) / total_verdicts

                # Update labels with enhanced information
                self.total_verdicts_label.setText(f"Total: {total_verdicts} ({len(active_verdicts)} active)")

                # Color-code success rate
                if success_rate >= 60:
                    success_color = MatrixTheme.GREEN
                elif success_rate >= 40:
                    success_color = MatrixTheme.YELLOW
                else:
                    success_color = MatrixTheme.RED

                self.success_rate_label.setText(f"Win Rate: {success_rate:.1f}%")
                self.success_rate_label.setStyleSheet(f"color: {success_color}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

                # Show average PnL instead of confidence
                if avg_pnl != 0:
                    pnl_color = MatrixTheme.GREEN if avg_pnl > 0 else MatrixTheme.RED
                    self.avg_confidence_label.setText(f"Avg PnL: {avg_pnl:+.1f}%")
                    self.avg_confidence_label.setStyleSheet(f"color: {pnl_color}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.avg_confidence_label.setText(f"Avg Conf: {avg_confidence:.1f}%")
                    self.avg_confidence_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            else:
                self.total_verdicts_label.setText("Total: 0")
                self.success_rate_label.setText("Win Rate: 0%")
                self.avg_confidence_label.setText("Avg PnL: 0%")

        except Exception as e:
            self.log_message(f"Error updating historical stats: {e}")

    def manually_close_verdict(self, verdict_id, exit_reason="MANUAL_EXIT"):
        """Manually close an active verdict for testing purposes"""
        try:
            if verdict_id in self.active_verdicts:
                verdict = self.active_verdicts[verdict_id]

                # Get current price
                current_symbol = self.symbol_combo.currentText()
                current_price = None

                if hasattr(self, 'live_data_manager') and self.live_data_manager:
                    current_price = self.live_data_manager.get_latest_price(current_symbol)

                if current_price is None and hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                    if self.current_bid and self.current_ask:
                        current_price = (self.current_bid + self.current_ask) / 2

                if current_price:
                    verdict['exit_price'] = current_price
                    verdict['exit_reason'] = exit_reason

                    # Calculate final PnL
                    entry_price = verdict['entry_price']
                    if verdict['verdict'] == 'LONG':
                        verdict['pnl'] = (current_price - entry_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((current_price - entry_price) / entry_price) * 100
                    elif verdict['verdict'] == 'SHORT':
                        verdict['pnl'] = (entry_price - current_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((entry_price - current_price) / entry_price) * 100

                    # Determine result
                    if verdict['pnl_percentage'] > 0:
                        verdict['result'] = 'WIN'
                    else:
                        verdict['result'] = 'LOSS'

                    verdict['is_active'] = False

                    # Remove from active tracking
                    del self.active_verdicts[verdict_id]

                    self.log_message(f"🔒 Manually closed verdict {verdict_id}: {verdict['result']} - PnL: {verdict['pnl_percentage']:+.1f}%")

                    # Update table
                    self.update_historical_verdicts_table()

                    return True

        except Exception as e:
            self.log_message(f"Error manually closing verdict: {e}")

        return False

    def update_verdict_tracking(self):
        """Update tracking for active verdicts based on current market prices"""
        try:
            if not self.active_verdicts:
                return

            # Get current symbol and price
            current_symbol = self.symbol_combo.currentText()
            current_price = None

            # Try to get current price from live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                current_price = self.live_data_manager.get_latest_price(current_symbol)

            # Fallback to bid/ask average if available
            if current_price is None and hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    current_price = (self.current_bid + self.current_ask) / 2

            if current_price is None:
                return

            # Track each active verdict
            completed_verdicts = []

            for verdict_id, verdict in self.active_verdicts.items():
                if verdict['symbol'] != current_symbol:
                    continue

                # Calculate duration
                from datetime import datetime
                duration = (datetime.now() - verdict['timestamp']).total_seconds() / 60
                verdict['duration_minutes'] = duration

                # Update max favorable/adverse prices
                entry_price = verdict['entry_price']

                if verdict['verdict'] == 'LONG':
                    # For LONG positions
                    if current_price > entry_price:
                        # Favorable move
                        verdict['max_favorable'] = max(verdict['max_favorable'], current_price - entry_price)
                    else:
                        # Adverse move
                        verdict['max_adverse'] = max(verdict['max_adverse'], entry_price - current_price)

                    # Check stop loss hit
                    if verdict['stop_loss'] > 0 and current_price <= verdict['stop_loss']:
                        verdict['result'] = 'LOSS'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'SL_HIT'
                        completed_verdicts.append(verdict_id)

                    # Check take profit hit
                    elif verdict['take_profit'] > 0 and current_price >= verdict['take_profit']:
                        verdict['result'] = 'WIN'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'TP_HIT'
                        completed_verdicts.append(verdict_id)

                elif verdict['verdict'] == 'SHORT':
                    # For SHORT positions
                    if current_price < entry_price:
                        # Favorable move
                        verdict['max_favorable'] = max(verdict['max_favorable'], entry_price - current_price)
                    else:
                        # Adverse move
                        verdict['max_adverse'] = max(verdict['max_adverse'], current_price - entry_price)

                    # Check stop loss hit
                    if verdict['stop_loss'] > 0 and current_price >= verdict['stop_loss']:
                        verdict['result'] = 'LOSS'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'SL_HIT'
                        completed_verdicts.append(verdict_id)

                    # Check take profit hit
                    elif verdict['take_profit'] > 0 and current_price <= verdict['take_profit']:
                        verdict['result'] = 'WIN'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'TP_HIT'
                        completed_verdicts.append(verdict_id)

                # Check for expiration (24 hours)
                if duration > 1440:  # 24 hours in minutes
                    verdict['result'] = 'EXPIRED'
                    verdict['exit_price'] = current_price
                    verdict['exit_reason'] = 'EXPIRED'
                    completed_verdicts.append(verdict_id)

                # Calculate current PnL (with division by zero protection)
                if verdict['exit_price']:
                    exit_price = verdict['exit_price']
                else:
                    exit_price = current_price

                # Protect against division by zero
                if entry_price > 0:
                    if verdict['verdict'] == 'LONG':
                        verdict['pnl'] = (exit_price - entry_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((exit_price - entry_price) / entry_price) * 100
                    elif verdict['verdict'] == 'SHORT':
                        verdict['pnl'] = (entry_price - exit_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((entry_price - exit_price) / entry_price) * 100
                else:
                    # If entry price is 0, set PnL to 0 to avoid division by zero
                    verdict['pnl'] = 0.0
                    verdict['pnl_percentage'] = 0.0

            # Remove completed verdicts from active tracking
            for verdict_id in completed_verdicts:
                verdict = self.active_verdicts[verdict_id]
                self.log_message(f"🏁 Verdict {verdict_id} completed: {verdict['result']} - {verdict['exit_reason']} - PnL: ${verdict['pnl']:.2f} ({verdict['pnl_percentage']:.1f}%)")
                del self.active_verdicts[verdict_id]

            # Update the historical verdicts table if any changes occurred
            if completed_verdicts or self.active_verdicts:
                self.update_historical_verdicts_table()

        except Exception as e:
            self.log_message(f"Error updating verdict tracking: {e}")

    def update_final_verdict_panel(self, trade_instruction):
        """Update the final verdict GUI panel with ScalperGPT trade instruction"""
        try:
            # Handle both old format (verdict_data) and new format (trade_instruction)
            if 'ACTION' in trade_instruction:
                # New ScalperGPT format
                action = trade_instruction.get('ACTION', 'WAIT')
                verdict = action.replace('BUY', 'LONG').replace('SELL', 'SHORT')
                confidence = trade_instruction.get('confidence', 85.0)
                quantity = trade_instruction.get('QUANTITY', 0)
                leverage = f"{trade_instruction.get('LEVERAGE', 1)}x"
                risk_pct = trade_instruction.get('RISK_PCT', 2.0)
                order_type = trade_instruction.get('ORDER_TYPE', 'MARKET')

                self.final_verdict_label.setText(f"ScalperGPT: {action} ({confidence:.1f}%)")

                # Update trading parameters with ScalperGPT data
                self.final_position_size_label.setText(f"{quantity:.4f}")
                self.final_stop_loss_label.setText(f"${trade_instruction.get('STOP_LOSS', 0):.6f}")
                self.final_take_profit_label.setText(f"${trade_instruction.get('TAKE_PROFIT', 0):.6f}")
                self.final_leverage_label.setText(leverage)
                self.final_risk_level_label.setText(f"{risk_pct:.1f}%")

                # Update confidence display if available
                if hasattr(self, 'scalper_confidence_label'):
                    self.scalper_confidence_label.setText(f"{confidence:.0f}%")

                # Update order type if available
                if hasattr(self, 'scalper_order_type_label'):
                    self.scalper_order_type_label.setText(order_type)

                # Update reasoning with ScalperGPT context
                reasoning = f"ScalperGPT Decision: {action} {quantity:.4f} @ {leverage} leverage, {risk_pct:.1f}% risk, {order_type} order"
                self.final_reasoning_text.setText(reasoning)

            else:
                # Legacy format
                verdict = trade_instruction.get('verdict', 'WAIT')
                confidence = trade_instruction.get('confidence', 0)

                self.final_verdict_label.setText(f"Final Verdict: {verdict} ({confidence:.1f}%)")

                # Update trading parameters (legacy format)
                self.final_position_size_label.setText(f"{trade_instruction.get('position_size', 0):.4f}")
                self.final_stop_loss_label.setText(f"${trade_instruction.get('stop_loss', 0):.6f}")
                self.final_take_profit_label.setText(f"${trade_instruction.get('take_profit', 0):.6f}")
                self.final_leverage_label.setText(trade_instruction.get('leverage', '1x'))
                self.final_risk_level_label.setText(trade_instruction.get('risk_level', 'LOW'))

                # Update confidence display if available
                if hasattr(self, 'scalper_confidence_label'):
                    self.scalper_confidence_label.setText(f"{confidence:.0f}%")

                # Update reasoning
                reasoning = trade_instruction.get('reasoning', 'No reasoning provided')
                self.final_reasoning_text.setText(reasoning)

            # Color code based on verdict/action
            if verdict in ["LONG", "BUY"] or action == "BUY":
                color = MatrixTheme.GREEN
            elif verdict in ["SHORT", "SELL"] or action == "SELL":
                color = MatrixTheme.RED
            else:
                color = MatrixTheme.YELLOW

            self.final_verdict_label.setStyleSheet(f"""
                font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
                font-weight: bold;
                color: {color};
                padding: 8px;
                border: 2px solid {color};
                border-radius: 5px;
                background-color: {MatrixTheme.BLACK};
                text-align: center;
            """)

            # Color code risk level
            if 'RISK_PCT' in trade_instruction:
                risk_pct = trade_instruction.get('RISK_PCT', 2.0)
                if risk_pct > 3.0:
                    risk_color = MatrixTheme.RED
                elif risk_pct > 1.5:
                    risk_color = MatrixTheme.YELLOW
                else:
                    risk_color = MatrixTheme.GREEN
            else:
                risk_level = trade_instruction.get('risk_level', 'LOW')
                if risk_level == "HIGH":
                    risk_color = MatrixTheme.RED
                elif risk_level == "MEDIUM":
                    risk_color = MatrixTheme.YELLOW
                else:
                    risk_color = MatrixTheme.GREEN

            self.final_risk_level_label.setStyleSheet(f"color: {risk_color}; font-weight: bold;")

        except Exception as e:
            self.log_message(f"Error updating final verdict panel: {e}")

    def update_scalper_gpt_gui(self, market_data, ensemble_analysis, trade_instruction):
        """Update all ScalperGPT GUI components with real-time data"""
        try:
            # Update ML Models panel with ensemble data
            self.update_ml_models_ensemble_display(ensemble_analysis)

            # Update Market Intelligence panel with enriched data
            self.update_market_intelligence_display(market_data)

            # Update Final Trading Verdict panel
            if trade_instruction:
                self.update_final_verdict_panel(trade_instruction)

            # Update ScalperGPT controls status
            self.update_scalper_controls_status()

        except Exception as e:
            self.log_message(f"Error updating ScalperGPT GUI: {e}")

    def update_ml_models_ensemble_display(self, ensemble_analysis):
        """Update ML models table with ensemble analysis data"""
        try:
            if not hasattr(self, 'ml_models_table'):
                return

            # Update ensemble metrics
            if hasattr(self, 'ensemble_vote_label'):
                self.ensemble_vote_label.setText(f"Vote: {ensemble_analysis.get('majority_vote', 'WAIT')}")

            if hasattr(self, 'ensemble_confidence_label'):
                avg_conf = ensemble_analysis.get('avg_confidence', 50.0)
                self.ensemble_confidence_label.setText(f"Avg: {avg_conf:.1f}%")

            if hasattr(self, 'ensemble_score_label'):
                score = ensemble_analysis.get('weighted_score', 0.0)
                self.ensemble_score_label.setText(f"Score: {score:+.2f}")

            if hasattr(self, 'ensemble_consensus_label'):
                consensus = ensemble_analysis.get('consensus_strength', 0.0)
                self.ensemble_consensus_label.setText(f"Consensus: {consensus:.0f}%")

            # Update individual model rows (if we have the data)
            # This would be called from the actual ML analysis with real model data

        except Exception as e:
            self.log_message(f"Error updating ML ensemble display: {e}")

    def update_market_intelligence_display(self, market_data):
        """Update Market Intelligence panel with enriched market data"""
        try:
            # Update spread information
            if hasattr(self, 'scalper_spread_label'):
                spread = market_data.get('spread', 0.0)
                self.scalper_spread_label.setText(f"Spread: ${spread:.6f}")

            if hasattr(self, 'spread_pct_label'):
                spread_pct = market_data.get('spread_pct', 0.0)
                self.spread_pct_label.setText(f"({spread_pct:.3f}%)")

            # Update tick analysis
            if hasattr(self, 'tick_atr_label'):
                tick_atr = market_data.get('tick_atr', 0.0)
                self.tick_atr_label.setText(f"Tick ATR: ${tick_atr:.6f}")

            if hasattr(self, 'trade_flow_label'):
                flow = market_data.get('trade_flow_imbalance', 0.0)
                self.trade_flow_label.setText(f"Flow: {flow:+.1f}%")
                # Color code based on flow direction
                if flow > 0:
                    self.trade_flow_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                elif flow < 0:
                    self.trade_flow_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.trade_flow_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            # Update volume momentum
            if hasattr(self, 'volume_momentum_label'):
                momentum = market_data.get('volume_momentum', 0.0)
                self.volume_momentum_label.setText(f"Vol Momentum: {momentum:+.1f}%")
                # Color code based on momentum
                if momentum > 0:
                    self.volume_momentum_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.volume_momentum_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            # Update data latency
            if hasattr(self, 'data_latency_label'):
                latency = market_data.get('data_latency_ms', 50.0)
                self.data_latency_label.setText(f"Latency: {latency:.0f}ms")
                # Color code based on latency
                if latency < 100:
                    self.data_latency_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                elif latency < 200:
                    self.data_latency_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.data_latency_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            # Update order book displays
            self.update_order_book_display(market_data)

        except Exception as e:
            self.log_message(f"Error updating market intelligence display: {e}")

    def update_order_book_display(self, market_data):
        """Update order book bid/ask displays"""
        try:
            # Update top 5 bids
            if hasattr(self, 'top_bids_text'):
                bids = market_data.get('top_5_bids', [])
                bids_text = ""
                for i, (price, volume) in enumerate(bids[:5], 1):
                    bids_text += f"{i}. ${price:.6f} × {volume:.1f}\n"
                self.top_bids_text.setText(bids_text.strip())

            # Update top 5 asks
            if hasattr(self, 'top_asks_text'):
                asks = market_data.get('top_5_asks', [])
                asks_text = ""
                for i, (price, volume) in enumerate(asks[:5], 1):
                    asks_text += f"{i}. ${price:.6f} × {volume:.1f}\n"
                self.top_asks_text.setText(asks_text.strip())

        except Exception as e:
            self.log_message(f"Error updating order book display: {e}")

    def update_scalper_controls_status(self):
        """Update ScalperGPT controls status display"""
        try:
            # Update daily trades counter
            if hasattr(self, 'scalper_trades_label'):
                daily_trades = self.autonomous_trading_stats.get('daily_trades', 0)
                max_trades = self.autonomous_trading_stats.get('max_daily_trades', 10)
                self.scalper_trades_label.setText(f"Trades: {daily_trades}/{max_trades}")

                # Color code based on usage
                if daily_trades >= max_trades:
                    self.scalper_trades_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                elif daily_trades >= max_trades * 0.8:
                    self.scalper_trades_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.scalper_trades_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            # Update win rate
            if hasattr(self, 'scalper_winrate_label'):
                win_rate = self.autonomous_trading_stats.get('win_rate', 0.0)
                self.scalper_winrate_label.setText(f"Win Rate: {win_rate:.0f}%")

                # Color code based on performance
                if win_rate >= 60:
                    self.scalper_winrate_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                elif win_rate >= 45:
                    self.scalper_winrate_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.scalper_winrate_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            # Update PnL
            if hasattr(self, 'scalper_pnl_label'):
                pnl = self.autonomous_trading_stats.get('total_pnl', 0.0)
                self.scalper_pnl_label.setText(f"PnL: ${pnl:.2f}")

                # Color code based on profit/loss
                if pnl > 0:
                    self.scalper_pnl_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                elif pnl < 0:
                    self.scalper_pnl_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.scalper_pnl_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

        except Exception as e:
            self.log_message(f"Error updating ScalperGPT controls status: {e}")

    def emergency_stop_trading(self):
        """Emergency stop for all autonomous trading"""
        try:
            self.autonomous_trading_enabled = False
            self.auto_trader_checkbox.setChecked(False)
            self.autonomous_trading_stats['emergency_stop_triggered'] = True

            self.log_message("🚨 EMERGENCY STOP ACTIVATED - All autonomous trading halted")
            self.statusBar().showMessage("🚨 EMERGENCY STOP - Autonomous trading disabled", 5000)

            # Update emergency stop button appearance
            self.emergency_stop_btn.setText("🚨 EMERGENCY STOP ACTIVE")
            self.emergency_stop_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #ff3333;
                    color: {MatrixTheme.BLACK};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 6px;
                    border: 2px solid #ff3333;
                    border-radius: 5px;
                }}
            """)

        except Exception as e:
            self.log_message(f"Error in emergency stop: {e}")

    # Comprehensive Analysis Methods
    def run_comprehensive_analysis(self, symbol, current_price, ml_decisions, ml_confidences,
                                  llm_decision, llm_confidence, llm_reasoning, current_model):
        """Run comprehensive second-stage analysis with all available data"""
        try:
            if not hasattr(self, 'lmstudio_runner') or not self.lmstudio_runner:
                return

            # Get additional market data
            current_bid = getattr(self, 'current_bid', current_price)
            current_ask = getattr(self, 'current_ask', current_price)
            spread = abs(current_ask - current_bid) if current_ask and current_bid else 0.0

            # Get technical analysis data (mock for now)
            tech_decision = "WAIT"  # This would come from technical analysis
            tech_confidence = 75.0
            mtf_decision = "LONG"   # This would come from multi-timeframe analysis
            mtf_confidence = 68.0

            # Create comprehensive analysis prompt
            comprehensive_prompt = f"""🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: {symbol}
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: ${current_price:.6f}
📈 Bid: ${current_bid:.6f} | Ask: ${current_ask:.6f} | Spread: ${spread:.6f}
⏰ Analysis Time: {self.get_current_timestamp()}

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANALYSIS
═══════════════════════════════════════════════════════════════════════════════
🔹 SVM Model: {ml_decisions[0]} ({ml_confidences[0]:.1f}% confidence)
🔹 Random Forest: {ml_decisions[1]} ({ml_confidences[1]:.1f}% confidence)
🔹 LSTM Neural Network: {ml_decisions[2]} ({ml_confidences[2]:.1f}% confidence)
📊 ML Ensemble Average: {sum(ml_confidences)/3:.1f}% confidence

═══════════════════════════════════════════════════════════════════════════════
🧠 INITIAL LLM ANALYSIS ({current_model})
═══════════════════════════════════════════════════════════════════════════════
🎯 Decision: {llm_decision} ({llm_confidence:.1f}% confidence)
💭 Reasoning: {llm_reasoning[:200]}...

═══════════════════════════════════════════════════════════════════════════════
📈 TECHNICAL & MULTI-TIMEFRAME SIGNALS
═══════════════════════════════════════════════════════════════════════════════
🔧 Technical Analysis: {tech_decision} ({tech_confidence:.1f}% confidence)
⏱️ Multi-Timeframe: {mtf_decision} ({mtf_confidence:.1f}% confidence)

═══════════════════════════════════════════════════════════════════════════════
🎭 CREATIVE SYNTHESIS CHALLENGE
═══════════════════════════════════════════════════════════════════════════════

You are the MASTER TRADING STRATEGIST for the Epinnox AI Trading System.

Your mission: Synthesize ALL the above intelligence into a CREATIVE, COMPREHENSIVE trading strategy that considers:

1. 🎪 CONSENSUS ANALYSIS: How do all signals align or conflict?
2. 🎨 CREATIVE RISK ASSESSMENT: What unique risks/opportunities do you see?
3. 🎯 STRATEGIC POSITIONING: What's the optimal position size and timing?
4. 🎲 SCENARIO PLANNING: Best/worst case scenarios and contingencies
5. 🎪 MARKET PSYCHOLOGY: What emotions are driving this market?

Respond in this EXACT format:
FINAL_DECISION: [LONG/SHORT/WAIT]
CONFIDENCE: [0-100]
POSITION_SIZE: [SMALL/MEDIUM/LARGE/NONE]
ENTRY_STRATEGY: [IMMEDIATE/GRADUAL/WAIT_FOR_DIP/WAIT_FOR_BREAKOUT]
RISK_LEVEL: [LOW/MEDIUM/HIGH/EXTREME]
CREATIVE_INSIGHT: [Your most creative market insight in 1-2 sentences]
SYNTHESIS: [Comprehensive reasoning combining all signals and your creative analysis]"""

            self.log_message("🎭 Starting Comprehensive Creative Analysis...")
            self.log_message("═" * 80)

            # Get comprehensive response
            comprehensive_response = self.lmstudio_runner.run_inference(
                prompt=comprehensive_prompt,
                temperature=0.8,  # Higher creativity
                max_tokens=500    # More detailed response
            )

            # Parse and display comprehensive analysis
            self.display_comprehensive_analysis(comprehensive_response, symbol, current_model)

        except Exception as e:
            self.log_message(f"❌ Error in comprehensive analysis: {e}")

    def get_current_timestamp(self):
        """Get formatted current timestamp"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

    def display_comprehensive_analysis(self, response, symbol, model):
        """Display comprehensive analysis in interface and terminal"""
        try:
            self.log_message("🎭 COMPREHENSIVE ANALYSIS COMPLETE")
            self.log_message("═" * 80)
            self.log_message(f"📝 Full Creative Response:")
            self.log_message(response)
            self.log_message("═" * 80)

            # Parse structured response
            parsed_data = self.parse_comprehensive_response(response)

            # Display in terminal with creative formatting
            self.display_creative_terminal_output(parsed_data, symbol, model)

            # Update GUI with comprehensive data
            self.update_comprehensive_gui(parsed_data)

        except Exception as e:
            self.log_message(f"❌ Error displaying comprehensive analysis: {e}")

    def parse_comprehensive_response(self, response):
        """Parse the structured comprehensive response"""
        parsed = {
            'final_decision': 'WAIT',
            'confidence': 50.0,
            'position_size': 'NONE',
            'entry_strategy': 'WAIT_FOR_SIGNAL',
            'risk_level': 'MEDIUM',
            'creative_insight': 'Analysis in progress...',
            'synthesis': 'Comprehensive analysis completed.'
        }

        if not response:
            return parsed

        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('FINAL_DECISION:'):
                parsed['final_decision'] = line.split(':', 1)[1].strip()
            elif line.startswith('CONFIDENCE:'):
                try:
                    parsed['confidence'] = float(line.split(':', 1)[1].strip())
                except:
                    pass
            elif line.startswith('POSITION_SIZE:'):
                parsed['position_size'] = line.split(':', 1)[1].strip()
            elif line.startswith('ENTRY_STRATEGY:'):
                parsed['entry_strategy'] = line.split(':', 1)[1].strip()
            elif line.startswith('RISK_LEVEL:'):
                parsed['risk_level'] = line.split(':', 1)[1].strip()
            elif line.startswith('CREATIVE_INSIGHT:'):
                parsed['creative_insight'] = line.split(':', 1)[1].strip()
            elif line.startswith('SYNTHESIS:'):
                parsed['synthesis'] = line.split(':', 1)[1].strip()

        return parsed

    def display_creative_terminal_output(self, parsed_data, symbol, model):
        """Display creative formatted output in terminal"""
        try:
            # Creative ASCII art header
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message("🎪           EPINNOX CREATIVE TRADING INTELLIGENCE REPORT           🎪")
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message(f"🎯 Symbol: {symbol} | Model: {model} | Time: {self.get_current_timestamp()}")
            self.log_message("🎭" + "─" * 78 + "🎭")

            # Decision with creative formatting
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']

            if decision == "LONG":
                decision_emoji = "🚀📈"
            elif decision == "SHORT":
                decision_emoji = "🔻📉"
            else:
                decision_emoji = "⏸️⚖️"

            self.log_message(f"🎯 FINAL DECISION: {decision_emoji} {decision} ({confidence:.1f}% confidence)")
            self.log_message(f"📊 POSITION SIZE: {parsed_data['position_size']}")
            self.log_message(f"⚡ ENTRY STRATEGY: {parsed_data['entry_strategy']}")
            self.log_message(f"⚠️ RISK LEVEL: {parsed_data['risk_level']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"💡 CREATIVE INSIGHT: {parsed_data['creative_insight']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"🧠 SYNTHESIS: {parsed_data['synthesis']}")
            self.log_message("🎭" + "═" * 78 + "🎭")

        except Exception as e:
            self.log_message(f"❌ Error in creative terminal display: {e}")

    def update_comprehensive_gui(self, parsed_data):
        """Update GUI with comprehensive analysis data"""
        try:
            # Update status bar with comprehensive info
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']
            risk = parsed_data['risk_level']

            status_msg = f"🎭 COMPREHENSIVE: {decision} ({confidence:.1f}%) | Risk: {risk} | Strategy: {parsed_data['entry_strategy']}"
            self.statusBar().showMessage(status_msg)

            # Add comprehensive analysis to the analysis log
            comprehensive_summary = f"""
🎭 COMPREHENSIVE ANALYSIS COMPLETE:
• Final Decision: {decision} ({confidence:.1f}% confidence)
• Position Size: {parsed_data['position_size']}
• Entry Strategy: {parsed_data['entry_strategy']}
• Risk Level: {parsed_data['risk_level']}
• Creative Insight: {parsed_data['creative_insight']}
"""
            print(comprehensive_summary)  # Log to terminal instead of removed analysis_log panel

            # Update comprehensive analysis panel
            if hasattr(self, 'comprehensive_decision_label'):
                decision_text = f"🎭 {decision} ({confidence:.1f}%) | {parsed_data['position_size']} | {parsed_data['risk_level']} Risk"
                self.comprehensive_decision_label.setText(decision_text)

            if hasattr(self, 'creative_insight_text'):
                insight_text = f"💡 CREATIVE INSIGHT:\n{parsed_data['creative_insight']}\n\n🧠 SYNTHESIS:\n{parsed_data['synthesis']}"
                self.creative_insight_text.setText(insight_text)

        except Exception as e:
            self.log_message(f"❌ Error updating comprehensive GUI: {e}")

    # LMStudio Model Management Methods
    def on_model_switch_requested(self, model_name: str):
        """Handle model switch request from model selector"""
        try:
            # Initialize LMStudio runner if not already done
            if not hasattr(self, 'lmstudio_runner'):
                self.setup_lmstudio_runner()

            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.switch_model(model_name)
                if success:
                    self.log_message(f"🔄 Switched to model: {model_name}")
                    self.statusBar().showMessage(f"Model switched to: {model_name}")
                else:
                    self.log_message(f"❌ Failed to switch to model: {model_name}")
            else:
                self.log_message("❌ LMStudio runner not available")

        except Exception as e:
            self.log_message(f"Error switching model: {e}")

    def on_model_refresh_requested(self):
        """Handle model refresh request from model selector"""
        try:
            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.refresh_models()
                if success:
                    models = self.lmstudio_runner.get_available_models()
                    if hasattr(self, 'model_selector'):
                        self.model_selector.update_models(models)
                        self.model_selector.set_connection_status(True)
                    self.log_message(f"🔄 Refreshed models: {len(models)} found")
                else:
                    if hasattr(self, 'model_selector'):
                        self.model_selector.set_connection_status(False)
                    self.log_message("❌ Failed to refresh models")
            else:
                self.setup_lmstudio_runner()

        except Exception as e:
            self.log_message(f"Error refreshing models: {e}")

    def setup_lmstudio_runner(self):
        """Setup LMStudio runner with model discovery"""
        try:
            from llama.lmstudio_runner import LMStudioRunner

            self.lmstudio_runner = LMStudioRunner()

            # Connect signals
            self.lmstudio_runner.model_changed.connect(self.on_lmstudio_model_changed)
            self.lmstudio_runner.models_discovered.connect(self.on_lmstudio_models_discovered)

            # Initial model discovery
            models = self.lmstudio_runner.get_available_models()
            current_model = self.lmstudio_runner.get_current_model()

            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                if current_model:
                    self.model_selector.set_current_model(current_model)
                self.model_selector.set_connection_status(len(models) > 0)

            self.log_message(f"✓ LMStudio runner initialized with {len(models)} models")

            # Initialize LLM Orchestrator after LMStudio runner is ready
            self.setup_llm_orchestrator()

        except Exception as e:
            self.log_message(f"Error setting up LMStudio runner: {e}")

    def setup_llm_orchestrator(self):
        """Setup LLM Orchestrator for multi-prompt AI trading system"""
        try:
            from core.llm_orchestrator import LLMPromptOrchestrator, TradingContext

            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                # Pass the main window (self) as the trading interface since it has all the trading methods
                self.llm_orchestrator = LLMPromptOrchestrator(self.lmstudio_runner, self)
                self.llm_orchestrator.set_main_window(self)
                self.log_message("🧠 LLM Orchestrator initialized - Multi-prompt AI system active")
                self.log_message("🎯 Available prompts: Emergency, Position Management, Profit Optimization, Market Regime, Risk Assessment, Entry Timing, Strategy Adaptation, Opportunity Scanner")
            else:
                self.log_message("⚠️ LLM Orchestrator disabled - LMStudio runner not available")
                self.llm_orchestrator = None

        except Exception as e:
            self.log_message(f"❌ Failed to initialize LLM Orchestrator: {e}")
            self.llm_orchestrator = None

    def on_lmstudio_model_changed(self, model_name: str):
        """Handle LMStudio model change signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.set_current_model(model_name)
            self.log_message(f"✓ LMStudio model changed to: {model_name}")

        except Exception as e:
            self.log_message(f"Error handling model change: {e}")

    def on_lmstudio_models_discovered(self, models: list):
        """Handle LMStudio models discovery signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                self.model_selector.set_connection_status(len(models) > 0)
            self.log_message(f"✓ Discovered {len(models)} LMStudio models")

        except Exception as e:
            self.log_message(f"Error handling models discovery: {e}")

    # Manual Trading Methods
    def place_limit_long(self):
        """Place a limit long order using best bid price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit LONG order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit LONG order")
                    self.statusBar().showMessage("Failed to place limit LONG order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'bids' not in ob or not ob['bids']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['bids'][0][0]  # Use best bid price

            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'buy', quantity, price, params)

            if result:
                self.log_message(f"Limit LONG: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit LONG placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit LONG order")
                self.statusBar().showMessage("Failed to place limit LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_limit_short(self):
        """Place a limit short order using best ask price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit SHORT order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit SHORT order")
                    self.statusBar().showMessage("Failed to place limit SHORT order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'asks' not in ob or not ob['asks']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['asks'][0][0]  # Use best ask price

            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'sell', quantity, price, params)

            if result:
                self.log_message(f"Limit SHORT: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit SHORT placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit SHORT order")
                self.statusBar().showMessage("Failed to place limit SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_long(self):
        """Place a market long order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market LONG executed", 3000)
                else:
                    self.log_message(f"Failed to place market LONG order")
                    self.statusBar().showMessage("Failed to place market LONG order", 3000)
                return

            # Fallback to original implementation
            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'buy', quantity, params)

            if result:
                self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market LONG executed", 3000)
            else:
                self.log_message(f"Failed to place market LONG order")
                self.statusBar().showMessage("Failed to place market LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_short(self):
        """Place a market short order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market SHORT executed", 3000)
                else:
                    self.log_message(f"Failed to place market SHORT order")
                    self.statusBar().showMessage("Failed to place market SHORT order", 3000)
                return

            # Fallback to original implementation
            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'sell', quantity, params)

            if result:
                self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market SHORT executed", 3000)
            else:
                self.log_message(f"Failed to place market SHORT order")
                self.statusBar().showMessage("Failed to place market SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def close_all_positions(self):
        """Close all open positions"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.close_all_positions()
                if count > 0:
                    self.log_message(f"Closed {count} positions")
                    self.statusBar().showMessage(f"Closed {count} positions", 3000)
                else:
                    self.log_message("No positions to close")
                    self.statusBar().showMessage("No positions to close", 3000)
                return

            # Fallback to original implementation
            result = close_all_positions()
            if result:
                self.log_message("All positions closed")
                self.statusBar().showMessage("All positions closed", 3000)
            else:
                self.log_message("Failed to close positions")
                self.statusBar().showMessage("Failed to close positions", 3000)
        except Exception as e:
            self.log_message(f"Error closing positions: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.cancel_all_orders()
                if count > 0:
                    self.log_message(f"Cancelled {count} orders")
                    self.statusBar().showMessage(f"Cancelled {count} orders", 3000)
                else:
                    self.log_message("No orders to cancel")
                    self.statusBar().showMessage("No orders to cancel", 3000)
                return

            # Fallback to original implementation
            result = cancel_all_orders()
            if result:
                self.log_message("All orders cancelled")
                self.statusBar().showMessage("All orders cancelled", 3000)
            else:
                self.log_message("Failed to cancel orders")
                self.statusBar().showMessage("Failed to cancel orders", 3000)
        except Exception as e:
            self.log_message(f"Error cancelling orders: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def on_leverage_changed(self, leverage: int):
        """Handle leverage spinbox value changes"""
        try:
            symbol = self.symbol_combo.currentText()

            # Update real trading interface leverage
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.set_leverage(symbol, leverage)
                if success:
                    self.log_message(f"🔧 Leverage updated to {leverage}x for {symbol}")
                else:
                    self.log_message(f"⚠️ Failed to update leverage to {leverage}x for {symbol}")

            # Update current leverage in real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_current_leverage(leverage)

        except Exception as e:
            self.log_message(f"Error updating leverage: {str(e)}")

    def on_auto_trader_toggled(self, state):
        """Handle autonomous trading toggle"""
        try:
            self.autonomous_trading_enabled = bool(state)

            if self.autonomous_trading_enabled:
                # Enable autonomous trading
                self.log_message("🤖 AUTONOMOUS TRADING ENABLED - AI will execute trades automatically")
                self.log_message("⚠️ WARNING: AI will place real trades based on analysis. Monitor carefully!")

                # Debug: Check real trading interface status
                if hasattr(self, 'real_trading') and self.real_trading:
                    self.log_message(f"🔍 Real trading interface available: {type(self.real_trading).__name__}")
                    self.log_message(f"🔍 Demo mode: {self.real_trading.is_demo_mode()}")
                    self.log_message(f"🔍 Connected: {self.real_trading.is_connected()}")
                else:
                    self.log_message("❌ Real trading interface not available")

                # Reset daily stats if it's a new day
                self.reset_daily_trading_stats_if_needed()

                # Check safety conditions
                if not self.check_autonomous_trading_safety():
                    self.auto_trader_checkbox.setChecked(False)
                    self.autonomous_trading_enabled = False
                    return

            else:
                # Disable autonomous trading
                self.log_message("🤖 AUTONOMOUS TRADING DISABLED - Manual control restored")

        except Exception as e:
            self.log_message(f"Error toggling auto trader: {str(e)}")

    def check_autonomous_trading_safety(self):
        """Check if autonomous trading is safe to enable"""
        try:
            # Check if emergency stop is triggered
            if self.autonomous_trading_stats['emergency_stop_triggered']:
                self.log_message("❌ Emergency stop is active. Cannot enable autonomous trading.")
                return False

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"❌ Daily trade limit reached ({self.autonomous_trading_stats['max_daily_trades']}). Cannot enable autonomous trading.")
                return False

            # Check if real trading interface is available
            if not hasattr(self, 'real_trading') or not self.real_trading:
                self.log_message("❌ Real trading interface not available. Cannot enable autonomous trading.")
                return False

            # Check account balance
            account_state = self.get_comprehensive_account_state()
            if account_state:
                balance_info = account_state.get('balance_info', {})
                free_balance = balance_info.get('free_balance', 0)
                self.log_message(f"🔍 Checking balance for autonomous trading: ${free_balance:.2f} USDT")
                if free_balance < 10:  # Minimum $10 balance
                    self.log_message(f"❌ Insufficient balance for autonomous trading (${free_balance:.2f} < $10.00 required).")
                    return False
            else:
                self.log_message("❌ Could not retrieve account state for balance check.")
                return False

            self.log_message("✅ Autonomous trading safety checks passed")
            return True

        except Exception as e:
            self.log_message(f"Error checking autonomous trading safety: {str(e)}")
            return False

    def reset_daily_trading_stats_if_needed(self):
        """Reset daily trading stats if it's a new day"""
        try:
            from datetime import datetime, date

            today = date.today()
            last_trade_date = None

            if self.autonomous_trading_stats['last_trade_time']:
                last_trade_date = self.autonomous_trading_stats['last_trade_time'].date()

            if last_trade_date != today:
                self.autonomous_trading_stats['daily_trades'] = 0
                self.log_message("📅 Daily trading stats reset for new day")

        except Exception as e:
            self.log_message(f"Error resetting daily stats: {str(e)}")

    def get_comprehensive_account_state(self):
        """Get comprehensive account state for LLM decision making"""
        try:
            account_state = {
                'timestamp': datetime.now().isoformat(),
                'balance_info': {},
                'open_positions': [],
                'recent_trades': [],
                'risk_metrics': {},
                'market_conditions': {},
                'trading_stats': {},
                'leverage_settings': {}
            }

            # Get balance information from futures account
            if hasattr(self, 'real_trading') and self.real_trading:
                # Get futures account balance specifically using the correct method
                balance = self.real_trading.get_balance_info()
                # self.log_message(f"🔍 Retrieved balance from real trading interface: {balance}")

                if balance:
                    usdt_info = balance.get('USDT', {})
                    free_balance = usdt_info.get('free', 0)
                    used_balance = usdt_info.get('used', 0)
                    total_balance = usdt_info.get('total', 0)

                    account_state['balance_info'] = {
                        'free_balance': free_balance,
                        'used_balance': used_balance,
                        'total_balance': total_balance,
                        'currency': 'USDT'
                    }

                    self.log_message(f"💰 Account Balance Info: Free=${free_balance:.2f}, Used=${used_balance:.2f}, Total=${total_balance:.2f}")
                else:
                    self.log_message("⚠️ No balance data received from real trading interface")
                    account_state['balance_info'] = {
                        'free_balance': 0,
                        'used_balance': 0,
                        'total_balance': 0,
                        'currency': 'USDT'
                    }

                # Get open positions using the correct method
                positions = self.real_trading.get_all_positions()
                # self.log_message(f"🔍 Retrieved positions data: {positions}")

                if positions:
                    for symbol, position_data in positions.items():
                        # self.log_message(f"🔍 Processing position for {symbol}: {position_data}")

                        # Check multiple possible size fields
                        size = 0
                        if 'contracts' in position_data:
                            size = position_data.get('contracts', 0)
                        elif 'size' in position_data:
                            size = position_data.get('size', 0)
                        elif 'amount' in position_data:
                            size = position_data.get('amount', 0)
                        elif 'quantity' in position_data:
                            size = position_data.get('quantity', 0)

                        if size != 0:  # Only include non-zero positions
                            account_state['open_positions'].append({
                                'symbol': symbol,
                                'side': position_data.get('side', 'unknown'),
                                'size': size,
                                'entry_price': position_data.get('entryPrice', position_data.get('contract_value', position_data.get('entry_price', position_data.get('average_price', 0)))),
                                'unrealized_pnl': position_data.get('unrealizedPnl', position_data.get('profit', position_data.get('unrealized_pnl', position_data.get('pnl', 0)))),
                                'realized_pnl': position_data.get('realizedPnl', position_data.get('realized_pnl', 0)),
                                'margin_used': position_data.get('initialMargin', position_data.get('margin_balance', position_data.get('margin', position_data.get('initial_margin', 0))))
                            })

            # Get recent trading history from historical verdicts
            recent_verdicts = self.historical_verdicts[-10:] if self.historical_verdicts else []
            account_state['recent_trades'] = [
                {
                    'time': verdict.get('timestamp', '').strftime('%H:%M:%S') if verdict.get('timestamp') else 'unknown',
                    'symbol': verdict.get('symbol', 'unknown'),
                    'verdict': verdict.get('verdict', 'unknown'),
                    'result': verdict.get('result', 'PENDING'),
                    'pnl_percentage': verdict.get('pnl_percentage', 0)
                }
                for verdict in recent_verdicts
            ]

            # Calculate risk metrics
            account_state['risk_metrics'] = self.calculate_risk_metrics()

            # Get market conditions
            account_state['market_conditions'] = self.get_market_conditions()

            # Get current leverage settings
            account_state['leverage_settings'] = {
                'current_leverage': self.leverage_spinbox.value(),
                'max_leverage': 125,
                'leverage_source': 'UI_spinbox_readonly'
            }

            # Get trading statistics
            account_state['trading_stats'] = {
                'total_historical_trades': len(self.historical_verdicts),
                'autonomous_trades_today': self.autonomous_trading_stats['daily_trades'],
                'max_daily_trades': self.autonomous_trading_stats['max_daily_trades'],
                'emergency_stop_active': self.autonomous_trading_stats['emergency_stop_triggered']
            }

            return account_state

        except Exception as e:
            self.log_message(f"Error getting comprehensive account state: {str(e)}")
            return None

    def calculate_risk_metrics(self):
        """Calculate comprehensive risk metrics"""
        try:
            risk_metrics = {
                'portfolio_risk': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'avg_pnl': 0.0,
                'correlation_risk': 'LOW',
                'liquidity_risk': 'LOW',
                'exposure_ratio': 0.0
            }

            if self.historical_verdicts:
                completed_trades = [v for v in self.historical_verdicts if v.get('result') in ['WIN', 'LOSS']]

                if completed_trades:
                    # Calculate win rate
                    wins = len([v for v in completed_trades if v.get('result') == 'WIN'])
                    risk_metrics['win_rate'] = (wins / len(completed_trades)) * 100

                    # Calculate average PnL
                    total_pnl = sum(v.get('pnl_percentage', 0) for v in completed_trades)
                    risk_metrics['avg_pnl'] = total_pnl / len(completed_trades)

                    # Calculate max drawdown (simplified)
                    pnl_values = [v.get('pnl_percentage', 0) for v in completed_trades]
                    if pnl_values:
                        cumulative_pnl = []
                        running_total = 0
                        for pnl in pnl_values:
                            running_total += pnl
                            cumulative_pnl.append(running_total)

                        peak = cumulative_pnl[0]
                        max_drawdown = 0
                        for value in cumulative_pnl:
                            if value > peak:
                                peak = value
                            drawdown = peak - value
                            if drawdown > max_drawdown:
                                max_drawdown = drawdown

                        risk_metrics['max_drawdown'] = max_drawdown

            return risk_metrics

        except Exception as e:
            self.log_message(f"Error calculating risk metrics: {str(e)}")
            return {}

    def get_market_conditions(self):
        """Get current market conditions"""
        try:
            market_conditions = {
                'volatility': 0.0,
                'trend_strength': 0.0,
                'liquidity_score': 0.0,
                'spread': 0.0,
                'market_regime': 'UNKNOWN'
            }

            # Get current market metrics
            trend_strength, volatility = self.calculate_market_metrics()
            market_conditions['trend_strength'] = trend_strength
            market_conditions['volatility'] = volatility

            # Get liquidity score
            liquidity_score = self.calculate_liquidity_score()
            market_conditions['liquidity_score'] = liquidity_score

            # Calculate spread
            if hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                    market_conditions['spread'] = spread_pct

            return market_conditions

        except Exception as e:
            self.log_message(f"Error getting market conditions: {str(e)}")
            return {}

    def execute_autonomous_trade(self, symbol, trade_instruction):
        """Execute autonomous trade based on ScalperGPT instruction with comprehensive safety checks"""
        try:
            self.log_message("🤖 AUTO TRADER: Starting ScalperGPT autonomous trade execution")
            self.log_message(f"🤖 AUTO TRADER: Symbol: {symbol}")
            self.log_message(f"🤖 AUTO TRADER: Trade Instruction: {trade_instruction}")

            # Get comprehensive account state for decision validation
            account_state = self.get_comprehensive_account_state()
            if not account_state:
                self.log_message("❌ AUTO TRADER: Cannot execute autonomous trade: Unable to get account state")
                return False

            # Extract trade parameters from LLM instruction
            action = trade_instruction.get('ACTION', 'WAIT')
            quantity = trade_instruction.get('QUANTITY', 0.0)
            leverage = trade_instruction.get('LEVERAGE', 1)
            risk_pct = trade_instruction.get('RISK_PCT', 2.0)
            order_type = trade_instruction.get('ORDER_TYPE', 'MARKET')

            self.log_message("🤖 ═══════════════════════════════════════════════════════════════")
            self.log_message("🤖 SCALPER GPT AUTONOMOUS EXECUTION")
            self.log_message(f"🤖 ACTION: {action} | QUANTITY: {quantity:.4f} | LEVERAGE: {leverage}x")
            self.log_message(f"🤖 RISK: {risk_pct}% | ORDER TYPE: {order_type}")
            self.log_message("🤖 ═══════════════════════════════════════════════════════════════")

            # Pre-execution safety checks with LLM instruction validation
            self.log_message("🔍 AUTO TRADER: Running pre-execution safety checks")
            if not self.pre_execution_safety_checks_llm(trade_instruction, account_state):
                self.log_message("❌ AUTO TRADER: Safety checks failed, aborting trade")
                return False
            self.log_message("✅ AUTO TRADER: Safety checks passed")

            # Use LLM-specified quantity directly (with safety constraints)
            final_quantity = self.validate_and_adjust_quantity(symbol, quantity, account_state)
            if final_quantity <= 0:
                self.log_message("❌ Final quantity is zero or negative. Skipping trade.")
                return False

            # Log comprehensive decision context
            self.log_autonomous_decision_context_llm(trade_instruction, account_state, final_quantity)

            # Execute the trade using LLM parameters
            self.log_message(f"🎯 AUTO TRADER: Executing {action} trade with {final_quantity:.4f} quantity")
            success = False
            if action == "BUY":
                self.log_message("📈 AUTO TRADER: Executing BUY position (ScalperGPT)")
                success = self.execute_autonomous_long_llm(symbol, final_quantity, leverage, order_type)
            elif action == "SELL":
                self.log_message("📉 AUTO TRADER: Executing SELL position (ScalperGPT)")
                success = self.execute_autonomous_short_llm(symbol, final_quantity, leverage, order_type)
            else:
                self.log_message(f"⏸️ AUTO TRADER: ScalperGPT Decision: {action} - No trade executed (WAIT)")
                return True  # WAIT is a valid decision

            # Update autonomous trading statistics
            if success:
                self.update_autonomous_trading_stats(True, trade_instruction)
                self.log_message("✅ ScalperGPT autonomous trade executed successfully")
            else:
                self.update_autonomous_trading_stats(False, trade_instruction)
                self.log_message("❌ ScalperGPT autonomous trade execution failed")

            return success

        except Exception as e:
            self.log_message(f"❌ Error in ScalperGPT autonomous trade execution: {str(e)}")
            return False

    def pre_execution_safety_checks_llm(self, trade_instruction, account_state):
        """Comprehensive pre-execution safety checks for LLM trade instructions"""
        try:
            # Check emergency stop
            if self.autonomous_trading_stats['emergency_stop_triggered']:
                self.log_message("❌ Emergency stop is active. Trade cancelled.")
                return False

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"❌ Daily trade limit reached ({self.autonomous_trading_stats['max_daily_trades']}). Trade cancelled.")
                self.autonomous_trading_enabled = False
                self.auto_trader_checkbox.setChecked(False)
                return False

            # Check minimum risk threshold
            risk_pct = trade_instruction.get('RISK_PCT', 0)
            if risk_pct < 0.5 or risk_pct > 5.0:
                self.log_message(f"❌ Risk percentage out of bounds ({risk_pct}%). Must be 0.5-5.0%.")
                return False

            # Check account balance
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            if free_balance < 20:  # Minimum $20 for autonomous trades
                self.log_message(f"❌ Insufficient balance (${free_balance:.2f}) for autonomous trade. Minimum $20 required.")
                return False

            # Check quantity bounds
            quantity = trade_instruction.get('QUANTITY', 0)
            if quantity <= 0:
                self.log_message(f"❌ Invalid quantity: {quantity}")
                return False

            # Check leverage bounds
            leverage = trade_instruction.get('LEVERAGE', 1)
            if leverage < 1 or leverage > 200:
                self.log_message(f"❌ Leverage out of bounds: {leverage}x (must be 1-200)")
                return False

            self.log_message("✅ All LLM pre-execution safety checks passed")
            return True

        except Exception as e:
            self.log_message(f"Error in LLM pre-execution safety checks: {str(e)}")
            return False

    def validate_and_adjust_quantity(self, symbol, quantity, account_state):
        """Validate and adjust LLM-specified quantity for safety"""
        try:
            # Get minimum order size for the symbol
            min_order_size = self.get_minimum_order_size(symbol)

            # Ensure quantity meets minimum requirements
            if quantity < min_order_size:
                self.log_message(f"⚠️ LLM quantity {quantity:.4f} below minimum {min_order_size:.4f}")
                # Adjust to minimum if close, otherwise reject
                if quantity >= min_order_size * 0.8:  # Within 20% of minimum
                    quantity = min_order_size
                    self.log_message(f"✅ Adjusted quantity to minimum: {quantity:.4f}")
                else:
                    self.log_message(f"❌ Quantity too small for minimum order requirements")
                    return 0

            # Check against account balance (safety cap)
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            max_position_value = free_balance * 0.15  # Max 15% of balance per trade

            # Estimate position value (simplified)
            current_price = getattr(self, 'current_bid', 0.17)  # Fallback price
            position_value = quantity * current_price

            if position_value > max_position_value:
                # Scale down quantity
                adjusted_quantity = max_position_value / current_price
                adjusted_quantity = max(min_order_size, adjusted_quantity)
                self.log_message(f"⚠️ Scaled down quantity from {quantity:.4f} to {adjusted_quantity:.4f} (balance safety)")
                quantity = adjusted_quantity

            # Round to appropriate precision
            precision = self.get_quantity_precision(symbol)
            final_quantity = round(quantity, precision)

            return final_quantity

        except Exception as e:
            self.log_message(f"Error validating quantity: {str(e)}")
            return 0

    def log_autonomous_decision_context_llm(self, trade_instruction, account_state, quantity):
        """Log comprehensive decision context for LLM trades"""
        try:
            balance = account_state.get('balance_info', {}).get('free_balance', 0)
            positions = len(account_state.get('open_positions', []))
            action = trade_instruction.get('ACTION', 'WAIT')
            leverage = trade_instruction.get('LEVERAGE', 1)
            risk_pct = trade_instruction.get('RISK_PCT', 2.0)
            daily_trades = self.autonomous_trading_stats['daily_trades']
            max_trades = self.autonomous_trading_stats['max_daily_trades']

            self.log_message(f"🧠 ScalperGPT Context: ${balance:.2f} balance, {positions} positions, {action} {quantity:.4f} @ {leverage}x, {risk_pct}% risk, {daily_trades}/{max_trades} trades")

        except Exception as e:
            self.log_message(f"Error logging LLM decision context: {str(e)}")

    def execute_autonomous_long_llm(self, symbol, quantity, leverage, order_type):
        """Execute autonomous long position using LLM parameters"""
        try:
            # Get current market data for intelligent order placement
            best_bid, best_ask = self.get_current_bid_ask(symbol)
            if not best_bid or not best_ask:
                self.log_message("❌ Cannot get current bid/ask prices for ScalperGPT LONG")
                return False

            # Choose entry price based on order type
            if order_type == "LIMIT":
                entry_price = best_ask  # Buy at ask for limit orders
            else:  # MARKET
                entry_price = best_ask  # Market orders also use ask

            self.log_message(f"🤖 ScalperGPT LONG: {quantity:.4f} {symbol} @ ${entry_price:.6f} ({order_type}, {leverage}x)")

            if hasattr(self, 'real_trading') and self.real_trading:
                # Set leverage first
                self.real_trading.set_leverage(symbol, leverage)

                # Execute order based on type
                if order_type == "LIMIT":
                    success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                else:
                    success = self.real_trading.place_market_long(symbol, quantity, leverage)

                if success:
                    self.log_message(f"✅ ScalperGPT LONG order placed successfully")
                    return True
                else:
                    self.log_message(f"❌ ScalperGPT LONG order failed")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing ScalperGPT long: {str(e)}")
            return False

    def execute_autonomous_short_llm(self, symbol, quantity, leverage, order_type):
        """Execute autonomous short position using LLM parameters"""
        try:
            # Get current market data for intelligent order placement
            best_bid, best_ask = self.get_current_bid_ask(symbol)
            if not best_bid or not best_ask:
                self.log_message("❌ Cannot get current bid/ask prices for ScalperGPT SHORT")
                return False

            # Choose entry price based on order type
            if order_type == "LIMIT":
                entry_price = best_bid  # Sell at bid for limit orders
            else:  # MARKET
                entry_price = best_bid  # Market orders also use bid

            self.log_message(f"🤖 ScalperGPT SHORT: {quantity:.4f} {symbol} @ ${entry_price:.6f} ({order_type}, {leverage}x)")

            if hasattr(self, 'real_trading') and self.real_trading:
                # Set leverage first
                self.real_trading.set_leverage(symbol, leverage)

                # Execute order based on type
                if order_type == "LIMIT":
                    success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                else:
                    success = self.real_trading.place_market_short(symbol, quantity, leverage)

                if success:
                    self.log_message(f"✅ ScalperGPT SHORT order placed successfully")
                    return True
                else:
                    self.log_message(f"❌ ScalperGPT SHORT order failed")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing ScalperGPT short: {str(e)}")
            return False

    def get_minimum_order_size(self, symbol):
        """Get minimum order size for the symbol"""
        try:
            # Default minimum order sizes for common symbols
            min_sizes = {
                'DOGE/USDT:USDT': 1.0,
                'BTC/USDT:USDT': 0.001,
                'ETH/USDT:USDT': 0.01,
                'default': 1.0
            }
            return min_sizes.get(symbol, min_sizes['default'])
        except:
            return 1.0

    def get_quantity_precision(self, symbol):
        """Get quantity precision for the symbol"""
        try:
            # Default precision for common symbols
            precisions = {
                'DOGE/USDT:USDT': 0,  # Whole numbers
                'BTC/USDT:USDT': 6,   # 6 decimal places
                'ETH/USDT:USDT': 4,   # 4 decimal places
                'default': 2
            }
            return precisions.get(symbol, precisions['default'])
        except:
            return 2

    def get_current_bid_ask(self, symbol):
        """Get current bid and ask prices for the symbol"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                order_book = self.live_data_manager.get_latest_orderbook(symbol)
                if order_book and 'bids' in order_book and 'asks' in order_book:
                    bids = order_book['bids']
                    asks = order_book['asks']
                    if bids and asks:
                        return float(bids[0][0]), float(asks[0][0])

            # Fallback to stored values
            bid = getattr(self, 'current_bid', None)
            ask = getattr(self, 'current_ask', None)
            return bid, ask
        except Exception as e:
            self.log_message(f"Error getting bid/ask: {e}")
            return None, None

    def pre_execution_safety_checks(self, verdict_data, account_state):
        """Comprehensive pre-execution safety checks"""
        try:
            # Check emergency stop
            if self.autonomous_trading_stats['emergency_stop_triggered']:
                self.log_message("❌ Emergency stop is active. Trade cancelled.")
                return False

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"❌ Daily trade limit reached ({self.autonomous_trading_stats['max_daily_trades']}). Trade cancelled.")
                self.autonomous_trading_enabled = False
                self.auto_trader_checkbox.setChecked(False)
                return False

            # Check minimum confidence threshold (lowered to 60% for more trading opportunities)
            confidence = verdict_data.get('confidence', 0)
            if confidence < 60:  # Minimum 60% confidence for autonomous trades
                self.log_message(f"❌ Confidence too low ({confidence}%) for autonomous trade. Minimum 60% required.")
                return False

            # Check account balance
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            if free_balance < 20:  # Minimum $20 for autonomous trades
                self.log_message(f"❌ Insufficient balance (${free_balance:.2f}) for autonomous trade. Minimum $20 required.")
                return False

            # Check for over-concentration
            if not self.check_position_concentration(account_state, verdict_data):
                return False

            # Check market conditions
            if not self.check_market_conditions_for_trading(account_state):
                return False

            self.log_message("✅ All pre-execution safety checks passed")
            return True

        except Exception as e:
            self.log_message(f"Error in pre-execution safety checks: {str(e)}")
            return False

    def calculate_intelligent_position_size(self, verdict_data, account_state):
        """Calculate intelligent position size based on account state, leverage, and risk management"""
        try:
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            confidence = verdict_data.get('confidence', 0)
            leverage = self.leverage_spinbox.value()

            # Enhanced base position size calculation considering leverage
            # Higher leverage = smaller base position to maintain same risk
            base_risk_pct = 3.0  # Increased from 2% for more aggressive sizing
            leverage_adjusted_risk = base_risk_pct / max(1, leverage / 10)  # Reduce risk as leverage increases
            base_position_value = free_balance * (leverage_adjusted_risk / 100)

            # Confidence multiplier (70-100% confidence maps to 0.7x-2.0x multiplier)
            confidence_multiplier = 0.7 + ((confidence - 70) / 30) * 1.3
            confidence_multiplier = max(0.7, min(2.0, confidence_multiplier))

            # Leverage efficiency multiplier (higher leverage allows smaller notional for same exposure)
            leverage_multiplier = min(1.5, 1.0 + (leverage - 1) * 0.05)  # Up to 1.5x for high leverage

            # Adjust based on current drawdown
            risk_metrics = account_state.get('risk_metrics', {})
            max_drawdown = risk_metrics.get('max_drawdown', 0)
            if max_drawdown > 15:  # Significant drawdown
                drawdown_multiplier = max(0.2, 1.0 - (max_drawdown / 100))
            elif max_drawdown > 5:  # Moderate drawdown
                drawdown_multiplier = max(0.5, 1.0 - (max_drawdown / 200))
            else:
                drawdown_multiplier = 1.0

            # Position count multiplier (more aggressive reduction for multiple positions)
            open_positions = account_state.get('open_positions', [])
            position_count = len(open_positions)
            if position_count == 0:
                position_count_multiplier = 1.2  # Bonus for first position
            elif position_count == 1:
                position_count_multiplier = 1.0  # Normal for second position
            else:
                position_count_multiplier = max(0.3, 1.0 - (position_count * 0.2))  # Reduce significantly

            # Market volatility multiplier (reduce size in high volatility)
            market_conditions = account_state.get('market_conditions', {})
            volatility = market_conditions.get('volatility', 2.0)
            if volatility > 8.0:  # Very high volatility
                volatility_multiplier = 0.5
            elif volatility > 5.0:  # High volatility
                volatility_multiplier = 0.7
            elif volatility > 3.0:  # Moderate volatility
                volatility_multiplier = 0.9
            else:
                volatility_multiplier = 1.0  # Low volatility

            # Calculate final position size with all multipliers
            final_position_value = (base_position_value *
                                  confidence_multiplier *
                                  leverage_multiplier *
                                  drawdown_multiplier *
                                  position_count_multiplier *
                                  volatility_multiplier)

            # Enhanced minimum and maximum limits based on leverage and balance
            min_position = max(3.0, free_balance * 0.005)  # Minimum 0.5% of balance or $3
            max_position = min(free_balance * 0.15, free_balance * (20 / leverage))  # Max 15% or leverage-adjusted

            final_position_value = max(min_position, min(max_position, final_position_value))

            # Clean position size logging
            self.log_message(f"🧮 Position Size: ${final_position_value:.2f} (Balance: ${free_balance:.2f}, Leverage: {leverage}x, Confidence: {confidence}%)")

            return final_position_value

        except Exception as e:
            self.log_message(f"Error calculating intelligent position size: {str(e)}")
            return 0

    def check_position_concentration(self, account_state, verdict_data):
        """Check for over-concentration in positions"""
        try:
            symbol = verdict_data.get('symbol', self.symbol_combo.currentText())
            open_positions = account_state.get('open_positions', [])

            # Check if already have position in same symbol
            existing_position = None
            for pos in open_positions:
                if pos['symbol'] == symbol:
                    existing_position = pos
                    break

            if existing_position:
                self.log_message(f"⚠️ Already have position in {symbol}: {existing_position['side']} {existing_position['size']}")
                # Allow if it's the same direction, reject if opposite
                verdict = verdict_data.get('verdict', 'WAIT')
                if (verdict == "LONG" and existing_position['side'] == 'short') or \
                   (verdict == "SHORT" and existing_position['side'] == 'long'):
                    self.log_message(f"❌ Cannot open {verdict} position - conflicts with existing {existing_position['side']} position")
                    return False

            # Check total exposure
            total_exposure = sum(abs(pos['size'] * pos['entry_price']) for pos in open_positions)
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)

            if total_exposure > free_balance * 0.8:  # Max 80% exposure
                self.log_message(f"❌ Total exposure too high: ${total_exposure:.2f} (max ${free_balance * 0.8:.2f})")
                return False

            return True

        except Exception as e:
            self.log_message(f"Error checking position concentration: {str(e)}")
            return False

    def check_market_conditions_for_trading(self, account_state):
        """Check if market conditions are suitable for trading"""
        try:
            market_conditions = account_state.get('market_conditions', {})

            # Check volatility (too high = dangerous)
            volatility = market_conditions.get('volatility', 0)
            if volatility > 5.0:  # 5% volatility threshold
                self.log_message(f"⚠️ High volatility ({volatility:.2f}%) - reducing trade aggressiveness")

            # Check liquidity
            liquidity_score = market_conditions.get('liquidity_score', 0)
            if liquidity_score < 0.3:  # Minimum liquidity threshold
                self.log_message(f"❌ Low liquidity ({liquidity_score:.2f}) - trade cancelled for safety")
                return False

            # Check spread
            spread = market_conditions.get('spread', 0)
            if spread > 0.5:  # 0.5% spread threshold
                self.log_message(f"⚠️ Wide spread ({spread:.3f}%) - may impact execution")

            return True

        except Exception as e:
            self.log_message(f"Error checking market conditions: {str(e)}")
            return True  # Default to allow trading if check fails

    def log_autonomous_decision_context(self, verdict_data, account_state, position_size):
        """Log comprehensive decision context for transparency"""
        try:
            # Clean context logging
            balance = account_state.get('balance_info', {}).get('free_balance', 0)
            positions = len(account_state.get('open_positions', []))
            confidence = verdict_data.get('confidence', 0)
            daily_trades = self.autonomous_trading_stats['daily_trades']
            max_trades = self.autonomous_trading_stats['max_daily_trades']

            self.log_message(f"🧠 Context: ${balance:.2f} balance, {positions} positions, {confidence:.0f}% confidence, ${position_size:.2f} size, {daily_trades}/{max_trades} trades")

        except Exception as e:
            self.log_message(f"Error logging decision context: {str(e)}")

    def execute_autonomous_long(self, symbol, position_size, verdict_data):
        """Execute autonomous long position using limit order with intelligent quantity calculation"""
        try:
            leverage = self.leverage_spinbox.value()

            # Get current market data for intelligent order placement
            best_bid, best_ask = self.get_current_bid_ask(symbol)
            if not best_bid or not best_ask:
                self.log_message("❌ Cannot get current bid/ask prices for autonomous LONG")
                return False

            # Calculate intelligent quantity based on USD position size and current price
            entry_price = best_ask  # Buy at ask price for LONG
            quantity = self.calculate_smart_quantity(position_size, entry_price, leverage, symbol)

            if quantity <= 0:
                self.log_message(f"❌ Invalid quantity calculated: {quantity}")
                return False

            # Execute limit long order using best ask price
            self.log_message(f"🤖 LONG: {quantity:.4f} {symbol} @ ${entry_price:.6f} (${position_size:.2f}, {leverage}x)")

            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ LONG order placed successfully")
                    return True
                else:
                    self.log_message(f"❌ LONG order failed")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing autonomous long: {str(e)}")
            return False

    def execute_autonomous_short(self, symbol, position_size, verdict_data):
        """Execute autonomous short position using limit order with intelligent quantity calculation"""
        try:
            leverage = self.leverage_spinbox.value()

            # Get current market data for intelligent order placement
            best_bid, best_ask = self.get_current_bid_ask(symbol)
            if not best_bid or not best_ask:
                self.log_message("❌ Cannot get current bid/ask prices for autonomous SHORT")
                return False

            # Calculate intelligent quantity based on USD position size and current price
            entry_price = best_bid  # Sell at bid price for SHORT
            quantity = self.calculate_smart_quantity(position_size, entry_price, leverage, symbol)

            if quantity <= 0:
                self.log_message(f"❌ Invalid quantity calculated: {quantity}")
                return False

            # Execute limit short order using best bid price
            self.log_message(f"🤖 SHORT: {quantity:.4f} {symbol} @ ${entry_price:.6f} (${position_size:.2f}, {leverage}x)")

            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ SHORT order placed successfully")
                    return True
                else:
                    self.log_message(f"❌ SHORT order failed")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing autonomous short: {str(e)}")
            return False

    def get_current_bid_ask(self, symbol):
        """Get current best bid and ask prices for the symbol"""
        try:
            # First try to get from live WebSocket data
            if hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid is not None and self.current_ask is not None:
                    current_symbol = self.symbol_combo.currentText()
                    if symbol == current_symbol:
                        return self.current_bid, self.current_ask

            # Fallback to real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                best_bid, best_ask = self.real_trading.get_best_bid_ask(symbol)
                if best_bid is not None and best_ask is not None:
                    return best_bid, best_ask

            self.log_message(f"⚠️ Could not get bid/ask prices for {symbol}")
            return None, None

        except Exception as e:
            self.log_message(f"Error getting bid/ask for {symbol}: {str(e)}")
            return None, None

    def calculate_smart_quantity(self, position_value_usd, entry_price, leverage, symbol):
        """Calculate smart quantity based on USD position value, price, and leverage"""
        try:
            # Basic quantity calculation: Position Value / Entry Price
            base_quantity = position_value_usd / entry_price

            # Get minimum order size for the symbol (HTX specific)
            min_order_size = self.get_minimum_order_size(symbol)

            # Ensure quantity meets minimum requirements
            if base_quantity < min_order_size:
                self.log_message(f"⚠️ Calculated quantity {base_quantity:.4f} below minimum {min_order_size:.4f}")
                # Adjust to minimum if close, otherwise reject
                if base_quantity >= min_order_size * 0.8:  # Within 20% of minimum
                    base_quantity = min_order_size
                    self.log_message(f"✅ Adjusted quantity to minimum: {base_quantity:.4f}")
                else:
                    self.log_message(f"❌ Position size too small for minimum order requirements")
                    return 0

            # Round to appropriate precision for the symbol
            precision = self.get_quantity_precision(symbol)
            final_quantity = round(base_quantity, precision)

            # Validate final quantity
            if final_quantity <= 0:
                self.log_message(f"❌ Final quantity is zero or negative: {final_quantity}")
                return 0

            # Clean quantity calculation logging (only if adjusted)
            if base_quantity != final_quantity:
                self.log_message(f"📊 Quantity adjusted: {base_quantity:.4f} → {final_quantity:.4f} (min: {min_order_size:.4f})")

            return final_quantity

        except Exception as e:
            self.log_message(f"Error calculating smart quantity: {str(e)}")
            return 0

    def get_minimum_order_size(self, symbol):
        """Get minimum order size for the symbol"""
        try:
            # HTX minimum order sizes (these are typical values, should be fetched from exchange info)
            min_sizes = {
                'DOGE/USDT:USDT': 1.0,     # 1 DOGE minimum
                'BTC/USDT:USDT': 0.0001,   # 0.0001 BTC minimum
                'ETH/USDT:USDT': 0.001,    # 0.001 ETH minimum
                'BNB/USDT:USDT': 0.01,     # 0.01 BNB minimum
                'ADA/USDT:USDT': 1.0,      # 1 ADA minimum
                'SOL/USDT:USDT': 0.01,     # 0.01 SOL minimum
            }

            return min_sizes.get(symbol, 1.0)  # Default to 1.0 if symbol not found

        except Exception as e:
            self.log_message(f"Error getting minimum order size for {symbol}: {str(e)}")
            return 1.0

    def get_quantity_precision(self, symbol):
        """Get quantity precision for the symbol"""
        try:
            # HTX quantity precisions (these are typical values)
            precisions = {
                'DOGE/USDT:USDT': 0,       # Whole numbers
                'BTC/USDT:USDT': 4,        # 4 decimal places
                'ETH/USDT:USDT': 3,        # 3 decimal places
                'BNB/USDT:USDT': 2,        # 2 decimal places
                'ADA/USDT:USDT': 0,        # Whole numbers
                'SOL/USDT:USDT': 2,        # 2 decimal places
            }

            return precisions.get(symbol, 2)  # Default to 2 decimal places

        except Exception as e:
            self.log_message(f"Error getting quantity precision for {symbol}: {str(e)}")
            return 2

    def update_autonomous_trading_stats(self, success, verdict_data):
        """Update autonomous trading statistics"""
        try:
            from datetime import datetime

            self.autonomous_trading_stats['total_trades'] += 1
            self.autonomous_trading_stats['daily_trades'] += 1
            self.autonomous_trading_stats['last_trade_time'] = datetime.now()

            if success:
                self.autonomous_trading_stats['successful_trades'] += 1

            # Check for emergency stop conditions
            self.check_emergency_stop_conditions()

        except Exception as e:
            self.log_message(f"Error updating autonomous trading stats: {str(e)}")

    def check_emergency_stop_conditions(self):
        """Check if emergency stop should be triggered"""
        try:
            # Check max drawdown
            risk_metrics = self.calculate_risk_metrics()
            max_drawdown = risk_metrics.get('max_drawdown', 0)

            if max_drawdown > self.autonomous_trading_stats['max_drawdown_limit']:
                self.trigger_emergency_stop(f"Max drawdown exceeded: {max_drawdown:.1f}%")
                return

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"⚠️ Daily trade limit reached. Disabling autonomous trading.")
                self.autonomous_trading_enabled = False
                self.auto_trader_checkbox.setChecked(False)
                return

        except Exception as e:
            self.log_message(f"Error checking emergency stop conditions: {str(e)}")

    def trigger_emergency_stop(self, reason):
        """Trigger emergency stop for autonomous trading"""
        try:
            self.autonomous_trading_stats['emergency_stop_triggered'] = True
            self.autonomous_trading_enabled = False
            self.auto_trader_checkbox.setChecked(False)

            self.log_message("🚨 ═══════════════════════════════════════════════════════════════")
            self.log_message("🚨 EMERGENCY STOP TRIGGERED")
            self.log_message("🚨 ═══════════════════════════════════════════════════════════════")
            self.log_message(f"🚨 Reason: {reason}")
            self.log_message("🚨 Autonomous trading has been disabled")
            self.log_message("🚨 Manual intervention required")

        except Exception as e:
            self.log_message(f"Error triggering emergency stop: {str(e)}")

    def update_chart(self):
        """🚀 PERFORMANCE FIX: Optimized chart update with incremental rendering"""
        try:
            # Only update if using fallback chart
            if not hasattr(self, 'chart_widget'):
                return

            # 🚀 PERFORMANCE FIX: Throttle chart updates to prevent excessive redraws
            current_time = time.time()
            if hasattr(self, 'last_chart_update'):
                if current_time - self.last_chart_update < 10:  # Minimum 10 seconds between updates
                    return

            symbol = self.symbol_combo.currentText()

            # Check if we have timeframe combo (fallback chart)
            if hasattr(self, 'timeframe_combo'):
                timeframe = self.timeframe_combo.currentText()
            else:
                timeframe = "1m"  # Default

            # Check if we have chart type combo (fallback chart)
            if hasattr(self, 'chart_type_combo'):
                chart_type = self.chart_type_combo.currentText()
            else:
                chart_type = "Line"  # Default

            # Fetch OHLCV data
            ohlcv_data = fetch_ohlcv(symbol, timeframe, 100)

            if not ohlcv_data:
                return

            # 🚀 PERFORMANCE FIX: Only update if data actually changed
            data_hash = hash(str(ohlcv_data[-5:]))  # Hash last 5 data points
            if hasattr(self, 'last_chart_data_hash') and self.last_chart_data_hash == data_hash:
                return

            self.last_chart_data_hash = data_hash
            self.last_chart_update = current_time

            # 🚀 PERFORMANCE FIX: Use incremental updates instead of full clear
            if not hasattr(self, 'chart_plot_item'):
                # First time - create plot item
                self.chart_widget.clear()
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
                self.chart_plot_item = self.chart_widget.plot([], [], pen=pen)

            # Extract data
            timestamps = [item[0] / 1000 for item in ohlcv_data]  # Convert to seconds
            prices = [item[4] for item in ohlcv_data]  # Close prices

            # 🚀 PERFORMANCE FIX: Update existing plot data instead of recreating
            self.chart_plot_item.setData(timestamps, prices)

            # 🚀 PERFORMANCE FIX: Only auto-scale if significant price change
            if not hasattr(self, 'last_price_range'):
                self.last_price_range = (min(prices), max(prices))
                self.chart_widget.getPlotItem().getViewBox().autoRange()
            else:
                current_range = (min(prices), max(prices))
                range_change = abs(current_range[1] - current_range[0]) / abs(self.last_price_range[1] - self.last_price_range[0])

                # Only auto-scale if price range changed significantly
                if range_change > 1.2 or range_change < 0.8:
                    self.chart_widget.getPlotItem().getViewBox().autoRange()
                    self.last_price_range = current_range

            # Update best bid/ask display (throttled)
            if current_time - getattr(self, 'last_bid_ask_update', 0) > 5:
                self.update_bid_ask_display()
                self.last_bid_ask_update = current_time

        except Exception as e:
            print(f"Error updating chart: {e}")

    def update_bid_ask_display(self):
        """Update best bid/ask display - ONLY called for fallback chart, WebSocket handles live updates"""
        try:
            # This method is now only used for fallback scenarios
            # Live WebSocket data is handled directly in on_live_orderbook_updated()
            # Only update if we don't have current bid/ask from WebSocket
            if not hasattr(self, 'current_bid') or self.current_bid is None:
                symbol = self.symbol_combo.currentText()

                # Use real trading interface if available
                if hasattr(self, 'real_trading') and self.real_trading:
                    best_bid, best_ask = self.real_trading.get_best_bid_ask(symbol)

                    if best_bid is not None:
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.current_ask = best_ask

            # Update display with current values (from WebSocket or fallback)
            if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                if self.current_bid is not None:
                    bid_text = f"{self.current_bid:.6f}"
                    if hasattr(self, 'last_bid') and self.last_bid is not None:
                        if self.current_bid > self.last_bid:
                            bid_text += " ↑"
                        elif self.current_bid < self.last_bid:
                            bid_text += " ↓"
                    self.best_bid_label.setText(bid_text)
                else:
                    self.best_bid_label.setText("--")

                if self.current_ask is not None:
                    ask_text = f"{self.current_ask:.6f}"
                    if hasattr(self, 'last_ask') and self.last_ask is not None:
                        if self.current_ask > self.last_ask:
                            ask_text += " ↑"
                        elif self.current_ask < self.last_ask:
                            ask_text += " ↓"
                    self.best_ask_label.setText(ask_text)
                else:
                    self.best_ask_label.setText("--")

                # Calculate and display spread
                if self.current_bid is not None and self.current_ask is not None:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                    self.spread_label.setText(f"{spread:.6f} ({spread_pct:.3f}%)")
                else:
                    self.spread_label.setText("--")

        except Exception as e:
            print(f"Error updating bid/ask display: {e}")
            if hasattr(self, 'best_bid_label'):
                self.best_bid_label.setText("--")
            if hasattr(self, 'best_ask_label'):
                self.best_ask_label.setText("--")
            if hasattr(self, 'spread_label'):
                self.spread_label.setText("--")

    def _set_trading_buttons_enabled(self, enabled: bool):
        """Enable/disable trading buttons (for loading states)"""
        try:
            self.limit_long_btn.setEnabled(enabled)
            self.market_long_btn.setEnabled(enabled)
            self.limit_short_btn.setEnabled(enabled)
            self.market_short_btn.setEnabled(enabled)
            self.close_all_btn.setEnabled(enabled)
            self.cancel_all_btn.setEnabled(enabled)

            # Update button text to show loading state
            if not enabled:
                buttons = [
                    (self.limit_long_btn, "PLACING..."),
                    (self.market_long_btn, "PLACING..."),
                    (self.limit_short_btn, "PLACING..."),
                    (self.market_short_btn, "PLACING..."),
                    (self.close_all_btn, "CLOSING..."),
                    (self.cancel_all_btn, "CANCELLING...")
                ]
                for btn, text in buttons:
                    btn.setText(text)
            else:
                # Restore original button text
                self.limit_long_btn.setText("LIMIT LONG")
                self.market_long_btn.setText("MARKET LONG")
                self.limit_short_btn.setText("LIMIT SHORT")
                self.market_short_btn.setText("MARKET SHORT")
                self.close_all_btn.setText("CLOSE ALL")
                self.cancel_all_btn.setText("CANCEL ALL")

        except Exception as e:
            print(f"Error setting button states: {e}")

    def on_chart_click(self, event):
        """Handle chart click events for order placement using best bid/ask"""
        try:
            if event.button() == 1:  # Left click - BUY (use best bid)
                self.place_limit_long()
            elif event.button() == 2:  # Right click - SELL (use best ask)
                self.place_limit_short()
            else:
                return

        except Exception as e:
            print(f"Error handling chart click: {e}")

    def on_live_chart_click(self, price: float, timestamp: float):
        """Handle live chart click events"""
        try:
            # For now, just trigger the same order placement logic
            # In the future, could use the clicked price for limit orders
            print(f"Live chart clicked at price: {price:.6f}, time: {timestamp}")

            # Use current best bid/ask instead of clicked price for safety
            self.place_limit_long()  # Default to long, could add right-click detection

        except Exception as e:
            print(f"Error handling live chart click: {e}")

    def on_chart_symbol_changed(self, symbol: str):
        """Handle symbol change from chart"""
        try:
            # Update main symbol selector
            self.symbol_combo.setCurrentText(symbol)

            # Subscribe to new symbol in live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                self.live_data_manager.subscribe_symbol(symbol, ["1m", "5m", "15m"])

            self.log_message(f"Chart symbol changed to: {symbol}")

        except Exception as e:
            print(f"Error handling chart symbol change: {e}")

    def on_chart_timeframe_changed(self, timeframe: str):
        """Handle timeframe change from chart"""
        try:
            self.log_message(f"Chart timeframe changed to: {timeframe}")

        except Exception as e:
            print(f"Error handling chart timeframe change: {e}")

    def on_live_chart_data_updated(self, symbol: str, chart_data: dict):
        """Handle live chart data updates"""
        try:
            if hasattr(self, 'live_chart'):
                # Update the live chart widget with new data
                ohlcv_data = chart_data.get("ohlcv", [])
                if ohlcv_data:
                    self.live_chart.update_ohlcv_data(ohlcv_data)

        except Exception as e:
            print(f"Error handling live chart data update: {e}")

    def on_live_price_updated(self, symbol: str, price: float):
        """Handle live price updates"""
        try:
            # Update current price display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                # Update any price displays
                pass

        except Exception as e:
            print(f"Error handling live price update: {e}")

    def on_live_orderbook_updated(self, symbol: str, orderbook_data: dict):
        """Handle live order book updates with enhanced manual trading panel integration"""
        try:
            # Update bid/ask display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                bids = orderbook_data.get("bids", [])
                asks = orderbook_data.get("asks", [])

                best_bid = bids[0][0] if bids else None
                best_ask = asks[0][0] if asks else None

                # Store the bid/ask values for manual trading
                if best_bid is not None:
                    self.last_bid = getattr(self, 'current_bid', None)
                    self.current_bid = best_bid
                if best_ask is not None:
                    self.last_ask = getattr(self, 'current_ask', None)
                    self.current_ask = best_ask

                # Update live chart bid/ask display
                if hasattr(self, 'live_chart'):
                    self.live_chart.update_bid_ask_data(best_bid, best_ask)

                # Update real trading interface with live orderbook data (if method exists)
                if hasattr(self, 'real_trading') and self.real_trading and hasattr(self.real_trading, 'update_live_orderbook'):
                    self.real_trading.update_live_orderbook(symbol, orderbook_data)

                # Update prediction tracker with current price
                if hasattr(self, 'prediction_tracker') and best_bid is not None:
                    self.prediction_tracker.update_current_price(symbol, best_bid)

                # Update manual trading panel with live WebSocket data ONLY
                if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                    # Update stored values for movement tracking
                    if best_bid is not None:
                        self.last_bid = getattr(self, 'current_bid', None)
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.last_ask = getattr(self, 'current_ask', None)
                        self.current_ask = best_ask

                    # Update bid display with movement indicator using batched updates
                    if self.current_bid is not None:
                        bid_text = f"{self.current_bid:.6f}"
                        if self.last_bid is not None:
                            if self.current_bid > self.last_bid:
                                bid_text += " ↑"
                            elif self.current_bid < self.last_bid:
                                bid_text += " ↓"
                        self.batch_gui_update('best_bid_label', 'text', bid_text)

                    # Update ask display with movement indicator using batched updates
                    if self.current_ask is not None:
                        ask_text = f"{self.current_ask:.6f}"
                        if self.last_ask is not None:
                            if self.current_ask > self.last_ask:
                                ask_text += " ↑"
                            elif self.current_ask < self.last_ask:
                                ask_text += " ↓"
                        self.batch_gui_update('best_ask_label', 'text', ask_text)

                    # Calculate and display spread using batched updates
                    if self.current_bid is not None and self.current_ask is not None:
                        spread = self.current_ask - self.current_bid
                        spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                        self.batch_gui_update('spread_label', 'text', f"{spread:.6f} ({spread_pct:.3f}%)")

                # Auto-update price spinbox if it's currently at default or old values
                if hasattr(self, 'price_spinbox') and best_bid and best_ask:
                    current_price = self.price_spinbox.value()
                    # If price is at default (0.175) or very different from market, suggest update
                    if current_price == 0.175 or abs(current_price - best_bid) / best_bid > 0.1:
                        # Set to mid-price as a reasonable default
                        mid_price = (best_bid + best_ask) / 2
                        self.price_spinbox.setValue(mid_price)

        except Exception as e:
            print(f"Error handling live orderbook update: {e}")

    def on_live_connection_status(self, connected: bool):
        """Handle live data connection status changes"""
        try:
            # Update live chart connection status
            if hasattr(self, 'live_chart'):
                self.live_chart.update_connection_status(connected)

            # Update status in log
            status = "Connected to live data" if connected else "Disconnected from live data"
            self.log_message(status)

        except Exception as e:
            print(f"Error handling live connection status: {e}")

    def on_trade_update(self, symbol: str, trade_data: dict):
        """Handle individual trade updates from WebSocket"""
        try:
            # Only process trades for the active chart symbol
            if hasattr(self, 'live_chart') and symbol == self.live_chart.current_symbol:
                trades = trade_data.get('trades', [])

                for trade in trades:
                    price = float(trade.get('price', 0))
                    amount = float(trade.get('amount', 0))
                    direction = trade.get('direction', 'buy')  # HTX uses 'buy'/'sell'
                    timestamp = float(trade.get('ts', 0)) / 1000  # Convert to seconds

                    # Update chart with individual trade
                    self.live_chart.update_trade_data(price, amount, direction, timestamp)

                    # Check if this might be our own trade (large volume)
                    if amount > 50:  # Trades > 50 units might be ours
                        # Trigger position refresh after a short delay
                        QTimer.singleShot(2000, self.refresh_orders_and_positions)

        except Exception as e:
            print(f"Error handling trade update: {e}")

    # Real Trading Interface Signal Handlers
    def on_order_status_updated(self, order_info: dict):
        """Handle order status updates from real trading interface"""
        try:
            order_type = order_info.get('type', '')
            order = order_info.get('order', {})

            if order_type == 'order_placed':
                self.log_message(f"Order placed: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')}")
            elif order_type == 'order_filled':
                self.log_message(f"Order filled: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')} @ {order.get('price', 0)}")
            elif order_type == 'order_cancelled':
                self.log_message(f"Order cancelled: {order.get('id', 'N/A')}")

        except Exception as e:
            print(f"Error handling order status update: {e}")

    def on_position_status_updated(self, position_info: dict):
        """Handle position status updates from real trading interface"""
        try:
            symbol = position_info.get('symbol', '')
            size = position_info.get('size', 0)
            side = position_info.get('side', '')
            pnl = position_info.get('unrealized_pnl', 0)

            if size != 0:
                # Throttle position update logging - only log significant changes
                import time
                if not hasattr(self, '_last_position_log'):
                    self._last_position_log = {}

                last_pnl = self._last_position_log.get(symbol, {}).get('pnl', 0)
                last_time = self._last_position_log.get(symbol, {}).get('time', 0)
                current_time = time.time()

                # Only log if PnL changed by >$0.10 or 30 seconds passed
                if abs(pnl - last_pnl) > 0.10 or current_time - last_time > 30:
                    self.log_message(f"Position update: {side} {abs(size)} {symbol} - PnL: ${pnl:.2f}")
                    self._last_position_log[symbol] = {'pnl': pnl, 'time': current_time}

                    # Trigger automatic position refresh when position changes
                    QTimer.singleShot(1000, self.refresh_orders_and_positions)

        except Exception as e:
            print(f"Error handling position status update: {e}")

    def on_balance_status_updated(self, balance_info: dict):
        """Handle balance status updates from real trading interface"""
        try:
            usdt_info = balance_info.get('USDT', {})
            free_balance = usdt_info.get('free', 0)
            total_balance = usdt_info.get('total', 0)

            # Update menu bar balance display (like in your image)
            if hasattr(self, 'balance_label'):
                balance_text = f"Equity: ${total_balance:.2f} Free: ${free_balance:.2f}"
                self.balance_label.setText(balance_text)

                # Color coding based on balance
                if free_balance > 0:
                    color = MatrixTheme.YELLOW  # Yellow for positive balance
                else:
                    color = MatrixTheme.RED  # Red for zero/negative balance

                self.balance_label.setStyleSheet(f"""
                    color: {color};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 2px 6px;
                    border: 1px solid {color};
                    border-radius: 3px;
                    background-color: rgba(255, 255, 0, 0.1);
                """)

            # Log balance update (less verbose)
            # self.log_message(f"💰 Balance: ${free_balance:.2f} USDT")

        except Exception as e:
            print(f"Error handling balance status update: {e}")

    def on_trading_error(self, error_message: str):
        """Handle trading errors from real trading interface"""
        try:
            self.log_message(f"Trading Error: {error_message}")
            self.statusBar().showMessage(f"Trading Error: {error_message}", 5000)

        except Exception as e:
            print(f"Error handling trading error: {e}")

    def on_trading_status(self, status_message: str):
        """Handle trading status updates from real trading interface"""
        try:
            self.log_message(f"Trading Status: {status_message}")

        except Exception as e:
            print(f"Error handling trading status: {e}")

    def on_pnl_updated(self, pnl_summary: dict):
        """Handle PnL updates from real trading interface"""
        try:
            unrealized = pnl_summary.get('unrealized_pnl', 0)
            realized = pnl_summary.get('realized_pnl', 0)
            total = pnl_summary.get('total_pnl', 0)

            # Update PnL display with throttling to prevent spam
            if total != 0:
                import time
                if not hasattr(self, '_last_pnl_log_time'):
                    self._last_pnl_log_time = 0
                    self._last_total_pnl = 0

                current_time = time.time()

                # Only log if PnL changed significantly or 30 seconds passed
                if (abs(total - self._last_total_pnl) > 0.10 or
                    current_time - self._last_pnl_log_time > 30):
                    self.log_message(f"PnL Update: Total ${total:.2f} (Unrealized: ${unrealized:.2f}, Realized: ${realized:.2f})")
                    self._last_pnl_log_time = current_time
                    self._last_total_pnl = total

        except Exception as e:
            print(f"Error handling PnL update: {e}")

    def on_risk_warning(self, warning_type: str, warning_message: str):
        """Handle risk warnings from real trading interface"""
        try:
            self.log_message(f"RISK WARNING [{warning_type.upper()}]: {warning_message}")
            self.statusBar().showMessage(f"RISK WARNING: {warning_message}", 10000)

        except Exception as e:
            print(f"Error handling risk warning: {e}")

    # Signal Trading Engine Signal Handlers
    def on_signal_received(self, signal_data: dict):
        """Handle signal received from signal trading engine"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            confidence = signal_data.get('confidence', 0)

            self.log_message(f"Signal received: {source} -> {decision} ({confidence:.1%})")

            # Record signal in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_signal(signal_data)

        except Exception as e:
            print(f"Error handling signal received: {e}")

    def on_trade_decision_made(self, decision_data: dict):
        """Handle trade decision from signal trading engine"""
        try:
            symbol = decision_data.get('symbol', '')
            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0)
            reasoning = decision_data.get('reasoning', '')

            self.log_message(f"Trade decision: {decision} {symbol} ({confidence:.1%}) - {reasoning}")

        except Exception as e:
            print(f"Error handling trade decision: {e}")

    def on_automated_trade_executed(self, trade_data: dict):
        """Handle automated trade execution"""
        try:
            symbol = trade_data.get('symbol', '')
            decision = trade_data.get('decision', '')
            position_size = trade_data.get('position_size', 0)
            confidence = trade_data.get('confidence', 0)

            self.log_message(f"🤖 Automated trade: {decision} {position_size} {symbol} (confidence: {confidence:.1%})")
            self.statusBar().showMessage(f"Automated {decision} executed", 5000)

            # Record trade in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_trade(trade_data)

            # Automatically refresh positions after trade execution
            QTimer.singleShot(3000, self.refresh_orders_and_positions)

        except Exception as e:
            print(f"Error handling automated trade: {e}")

    def on_risk_limit_triggered(self, limit_type: str, details: dict):
        """Handle risk limit triggered"""
        try:
            self.log_message(f"🚨 RISK LIMIT TRIGGERED: {limit_type} - {details}")
            self.statusBar().showMessage(f"RISK LIMIT: {limit_type}", 10000)

        except Exception as e:
            print(f"Error handling risk limit: {e}")

    def on_engine_status_changed(self, status: str):
        """Handle signal trading engine status changes"""
        try:
            self.log_message(f"Engine status: {status}")

        except Exception as e:
            print(f"Error handling engine status: {e}")

    # Session Management Signal Handlers
    def on_session_started(self, session_id: str):
        """Handle session started"""
        try:
            self.log_message(f"📊 Session started: {session_id}")

        except Exception as e:
            print(f"Error handling session started: {e}")

    def on_session_ended(self, session_id: str, summary: dict):
        """Handle session ended"""
        try:
            trade_stats = summary.get('trade_stats', {})
            total_trades = trade_stats.get('total_trades', 0)
            win_rate = trade_stats.get('win_rate', 0)
            total_pnl = trade_stats.get('total_pnl', 0)

            self.log_message(f"📊 Session ended: {session_id}")
            self.log_message(f"📈 Summary: {total_trades} trades, {win_rate:.1f}% win rate, ${total_pnl:.2f} PnL")

        except Exception as e:
            print(f"Error handling session ended: {e}")

    def on_trade_recorded_to_db(self, trade_data: dict):
        """Handle trade recorded to database"""
        try:
            trade_id = trade_data.get('trade_id', 'N/A')
            pnl = trade_data.get('pnl', 0)
            self.log_message(f"💾 Trade recorded: {trade_id} (PnL: ${pnl:.2f})")

        except Exception as e:
            print(f"Error handling trade recorded: {e}")

    def on_signal_recorded_to_db(self, signal_data: dict):
        """Handle signal recorded to database"""
        try:
            # Signal data available for future processing
            if signal_data:  # Use signal_data to avoid unused parameter warning
                # source = signal_data.get('source', 'unknown')
                # decision = signal_data.get('decision', 'WAIT')
                # Log at debug level to avoid spam
                # self.log_message(f"💾 Signal recorded: {source} -> {decision}")
                pass

        except Exception as e:
            print(f"Error handling signal recorded: {e}")

    # Dynamic Symbol Scanner Methods
    def on_dynamic_scan_toggled(self, state):
        """Handle dynamic scanner checkbox toggle - MAIN THREAD ONLY"""
        try:
            # Ensure we're on the main thread
            if not self.thread() == QApplication.instance().thread():
                self.log_message("⚠️ Scanner toggle called from non-main thread, deferring...")
                QTimer.singleShot(0, lambda: self.on_dynamic_scan_toggled(state))
                return

            if not hasattr(self, 'symbol_scanner') or not self.symbol_scanner:
                self.log_message("❌ Symbol scanner not available")
                self.dynamic_scan_cb.setChecked(False)
                return

            if state == Qt.Checked:
                # Enable dynamic scanning
                self.scanner_enabled = True

                # Ensure timer is created on main thread
                if not hasattr(self, 'scanner_timer') or self.scanner_timer is None:
                    self.scanner_timer = QTimer(self)  # Parent to main window
                    self.scanner_timer.timeout.connect(self.on_scan_tick)
                    self.scanner_timer.setSingleShot(False)

                self.scanner_timer.start(5000)  # Scan every 5 seconds
                self.scanner_status_label.setText("Scanner: Active")
                self.scanner_status_label.setStyleSheet(f"""
                    color: {MatrixTheme.GREEN};
                    font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
                    font-weight: bold;
                    padding: 2px;
                """)
                self.log_message("🤖 Dynamic symbol scanner enabled (main thread)")

                # Perform initial scan after short delay
                QTimer.singleShot(1000, self.on_scan_tick)

            else:
                # Disable dynamic scanning
                self.scanner_enabled = False
                if hasattr(self, 'scanner_timer') and self.scanner_timer:
                    self.scanner_timer.stop()
                self.scanner_status_label.setText("Scanner: Disabled")
                self.scanner_status_label.setStyleSheet(f"""
                    color: {MatrixTheme.YELLOW};
                    font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
                    padding: 2px;
                """)
                self.log_message("🤖 Dynamic symbol scanner disabled")

        except Exception as e:
            self.log_message(f"❌ Error toggling dynamic scanner: {e}")

    def on_scan_tick(self):
        """Handle scanner timer tick - find and select best symbol"""
        try:
            if not self.scanner_enabled or not hasattr(self, 'symbol_scanner') or not self.symbol_scanner:
                return

            # Check if a manual change happened recently (within 10 seconds)
            current_time = time.time()
            if hasattr(self, 'last_manual_change') and (current_time - self.last_manual_change) < 10:
                self.log_message("⏸️ Scanner paused - recent manual symbol change")
                return

            # Find best symbol
            best_symbols = self.symbol_scanner.find_best(n=1)

            if best_symbols:
                best_symbol = best_symbols[0]
                current_symbol = self.symbol_combo.currentText()

                # Update symbol if it changed
                if best_symbol != current_symbol:
                    self.log_message(f"🎯 Scanner switching: {current_symbol} → {best_symbol}")

                    # CRITICAL: Unsubscribe from old symbol first
                    if hasattr(self, 'live_data_manager') and self.live_data_manager:
                        try:
                            self.live_data_manager.unsubscribe_symbol(current_symbol)
                            self.log_message(f"📤 Unsubscribed from {current_symbol}")
                        except Exception as e:
                            self.log_message(f"⚠️ Error unsubscribing from {current_symbol}: {e}")

                    # Update symbol combo and current symbol
                    self.symbol_combo.setCurrentText(best_symbol)
                    self.current_symbol = best_symbol

                    # CRITICAL: Subscribe to new symbol with all timeframes
                    if hasattr(self, 'live_data_manager') and self.live_data_manager:
                        try:
                            self.live_data_manager.subscribe_symbol(best_symbol, ["1m", "5m", "15m"])
                            self.log_message(f"📥 Subscribed to {best_symbol}")
                        except Exception as e:
                            self.log_message(f"❌ Error subscribing to {best_symbol}: {e}")

                    # Update scanner status with current best symbol
                    metrics = self.symbol_scanner.get_symbol_metrics(best_symbol)
                    if metrics:
                        self.scanner_status_label.setText(f"Scanner: {best_symbol} ({metrics.score:.1f})")
                        self.log_message(f"📊 {best_symbol} score: {metrics.score:.1f} (spread: {metrics.spread_pct:.3f}%, depth: {metrics.orderbook_depth:.0f})")
                    else:
                        self.scanner_status_label.setText(f"Scanner: {best_symbol}")

                    # Notify user
                    self.statusBar().showMessage(f"🤖 Auto-selected: {best_symbol}", 3000)

                    # Force refresh of market data for new symbol
                    if hasattr(self, 'update_market_data'):
                        QTimer.singleShot(1000, self.update_market_data)  # Wait 1s for subscription

                    # Update any chart displays
                    if hasattr(self, 'update_chart_symbol'):
                        QTimer.singleShot(1500, lambda: self.update_chart_symbol(best_symbol))

                    # Clear any cached market data for the old symbol
                    if hasattr(self, 'clear_symbol_cache'):
                        self.clear_symbol_cache(current_symbol)

                else:
                    # Update status with current symbol score
                    metrics = self.symbol_scanner.get_symbol_metrics(current_symbol)
                    if metrics:
                        self.scanner_status_label.setText(f"Scanner: {current_symbol} ({metrics.score:.1f})")
            else:
                self.log_message("⚠️ Scanner found no suitable symbols")

        except Exception as e:
            self.log_message(f"❌ Error in scanner tick: {e}")

    def get_scanner_summary(self):
        """Get summary of scanner status for display"""
        try:
            if not hasattr(self, 'symbol_scanner') or not self.symbol_scanner:
                return "Scanner: Not Available"

            if not self.scanner_enabled:
                return "Scanner: Disabled"

            summary = self.symbol_scanner.get_scan_summary()
            if summary['status'] == 'success':
                return f"Scanner: {summary['symbols_scanned']} symbols, best: {summary['best_symbol']}"
            else:
                return "Scanner: No Data"

        except Exception as e:
            return f"Scanner: Error ({str(e)[:20]})"

    def clear_symbol_cache(self, symbol: str):
        """Clear cached data for a symbol when switching"""
        try:
            # Clear any cached market data
            if hasattr(self, 'market_data_cache'):
                self.market_data_cache.pop(symbol, None)

            # Clear any cached analysis results
            if hasattr(self, 'analysis_cache'):
                self.analysis_cache.pop(symbol, None)

            self.log_message(f"🧹 Cleared cache for {symbol}")

        except Exception as e:
            self.log_message(f"⚠️ Error clearing cache for {symbol}: {e}")

    def update_chart_symbol(self, symbol: str):
        """Update chart displays for new symbol"""
        try:
            # Update any chart widgets that need to know about symbol changes
            if hasattr(self, 'chart_widget') and self.chart_widget:
                # Clear old data and prepare for new symbol
                self.chart_widget.clear()
                self.log_message(f"📊 Chart updated for {symbol}")

            # Update any other symbol-dependent displays
            if hasattr(self, 'update_symbol_displays'):
                self.update_symbol_displays(symbol)

        except Exception as e:
            self.log_message(f"⚠️ Error updating chart for {symbol}: {e}")

    def on_manual_symbol_changed(self, new_symbol: str):
        """Handle manual symbol change from dropdown - CRITICAL FIX"""
        try:
            if not new_symbol or new_symbol.strip() == "":
                return

            # Get current symbol before change
            current_symbol = getattr(self, 'current_symbol', 'DOGE/USDT:USDT')

            # Skip if same symbol
            if new_symbol == current_symbol:
                return

            self.log_message(f"🔄 Manual symbol change: {current_symbol} → {new_symbol}")

            # CRITICAL: Unsubscribe from old symbol first
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                try:
                    self.live_data_manager.unsubscribe_symbol(current_symbol)
                    self.log_message(f"📤 Unsubscribed from {current_symbol}")
                except Exception as e:
                    self.log_message(f"⚠️ Error unsubscribing from {current_symbol}: {e}")

            # Update current symbol and timestamp
            self.current_symbol = new_symbol
            self.last_manual_change = time.time()  # Record manual change time

            # CRITICAL: Subscribe to new symbol with all timeframes
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                try:
                    self.live_data_manager.subscribe_symbol(new_symbol, ["1m", "5m", "15m"])
                    self.log_message(f"📥 Subscribed to {new_symbol}")
                except Exception as e:
                    self.log_message(f"❌ Error subscribing to {new_symbol}: {e}")

            # Clear cached data for old symbol
            self.clear_symbol_cache(current_symbol)

            # Update chart displays
            QTimer.singleShot(1000, lambda: self.update_chart_symbol(new_symbol))

            # Force refresh of market data
            if hasattr(self, 'update_market_data'):
                QTimer.singleShot(1500, self.update_market_data)

            # Update any other symbol-dependent components
            if hasattr(self, 'update_symbol_displays'):
                QTimer.singleShot(2000, lambda: self.update_symbol_displays(new_symbol))

            # Notify user
            self.statusBar().showMessage(f"📊 Switched to {new_symbol}", 3000)

            # If scanner is active, temporarily disable it to prevent conflicts
            if hasattr(self, 'scanner_enabled') and self.scanner_enabled:
                self.log_message("⏸️ Temporarily pausing scanner due to manual change")
                QTimer.singleShot(5000, self.resume_scanner_if_enabled)  # Resume after 5s

        except Exception as e:
            self.log_message(f"❌ Error in manual symbol change: {e}")

    def resume_scanner_if_enabled(self):
        """Resume scanner if it was enabled before manual change"""
        try:
            if hasattr(self, 'dynamic_scan_cb') and self.dynamic_scan_cb.isChecked():
                self.log_message("▶️ Resuming scanner after manual change")
                # Scanner will continue on next tick
        except Exception as e:
            self.log_message(f"⚠️ Error resuming scanner: {e}")

    def log_message(self, message):
        """Log message to console"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def main():
    """Main application entry point"""
    try:
        print("Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxTradingInterface()
        window.show()
        
        print("✓ Epinnox v6 GUI started successfully")
        print("✓ Ready for trading analysis")
        
        # Run application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ Error starting Epinnox v6: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()