# 🚀 Epinnox v6 - Autonomous AI Trading System

**The World's Most Advanced Autonomous Cryptocurrency Trading Platform**

Epinnox v6 is a fully autonomous, AI-driven cryptocurrency trading system that combines cutting-edge machine learning, reinforcement learning, large language models, and sophisticated risk management to execute trades without human intervention.

## 🌟 Key Features

### 🤖 **Fully Autonomous Trading**
- **Zero Human Intervention**: Complete autonomous decision-making and execution
- **AI-Driven Analysis**: Advanced LLM integration for market sentiment and analysis
- **Real-time Processing**: Continuous market monitoring and instant decision execution
- **Multi-Exchange Support**: HTX (Huobi), with extensible architecture for other exchanges

### 🧠 **Advanced AI & Machine Learning**
- **Ensemble ML Models**: 8 different ML models (SVM, Random Forest, LSTM, RSI, VWAP, Orderflow, Volatility, Sentiment)
- **Reinforcement Learning**: PPO/SAC agents for adaptive trading strategies
- **Online Learning**: Real-time model adaptation based on performance feedback
- **LLM Integration**: Local LLaMA/Phi models for comprehensive market analysis

### 📊 **Intelligent Portfolio Management**
- **Dynamic Position Sizing**: AI-optimized position sizing based on confidence and market conditions
- **Advanced Risk Management**: Multi-layer risk controls with real-time monitoring
- **Portfolio Rebalancing**: Automatic portfolio optimization and rebalancing
- **Performance Tracking**: Comprehensive trade analysis and performance metrics

### 🎯 **Dynamic Symbol Selection**
- **Real-time Symbol Scanning**: Automated discovery of optimal trading opportunities
- **Market Condition Analysis**: Spread, volatility, liquidity, and orderflow analysis
- **Adaptive Selection**: Dynamic switching between symbols based on market conditions

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    EPINNOX v6 ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────────┤
│  🎯 AUTONOMOUS CONTROLLER                                       │
│  ├── Decision Engine (LLM + ML Ensemble)                       │
│  ├── Risk Management (Multi-layer Safety)                      │
│  └── Execution Engine (Smart Order Management)                 │
├─────────────────────────────────────────────────────────────────┤
│  🧠 AI/ML LAYER                                                │
│  ├── Reinforcement Learning (PPO/SAC Agents)                   │
│  ├── Online Learning Manager (Adaptive Weights)                │
│  ├── ML Model Ensemble (8 Models)                              │
│  └── LLM Analysis (Local LLaMA/Phi)                           │
├─────────────────────────────────────────────────────────────────┤
│  📊 DATA & ANALYSIS                                            │
│  ├── Real-time Market Data (WebSocket)                         │
│  ├── Technical Indicators (50+ Indicators)                     │
│  ├── Symbol Scanner (Dynamic Selection)                        │
│  └── Performance Tracker (Trade Analytics)                     │
├─────────────────────────────────────────────────────────────────┤
│  💼 PORTFOLIO & RISK                                           │
│  ├── Portfolio Manager (Position Tracking)                     │
│  ├── Risk Manager (Multi-layer Controls)                       │
│  ├── Position Sizer (Dynamic Sizing)                          │
│  └── Leverage Manager (Adaptive Leverage)                      │
├─────────────────────────────────────────────────────────────────┤
│  🔧 INFRASTRUCTURE                                             │
│  ├── Configuration Management (Unified Config)                 │
│  ├── Exchange Integration (CCXT + Custom)                      │
│  ├── Database (SQLite + Performance Tracking)                  │
│  └── Logging & Monitoring (Comprehensive Logs)                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- 8GB+ RAM (16GB recommended)
- GPU support (optional, for faster ML training)
- Stable internet connection

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd Epinnox_v6
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure credentials**
```bash
# Copy and edit credentials template
cp credentials_template.yaml credentials.yaml
# Edit with your exchange API keys
```

4. **Run paper trading (recommended first)**
```bash
python start_paper_trading.py --balance 10000
```

5. **Run live trading (after testing)**
```bash
python start_paper_trading.py --live --balance 100
```

## 📁 Project Structure

```
Epinnox_v6/
├── 🎯 CORE AUTONOMOUS SYSTEM
│   ├── core/
│   │   ├── autonomous_controller.py      # Main autonomous trading controller
│   │   ├── autonomous_trading_orchestrator.py  # System orchestration
│   │   ├── adaptive_risk.py              # Adaptive risk management
│   │   ├── signal_hierarchy.py           # Intelligent signal processing
│   │   └── leverage_manager.py           # Dynamic leverage management
│   │
├── 🤖 EXECUTION ENGINE
│   ├── execution/
│   │   └── autonomous_executor.py        # Autonomous trade execution
│   │
├── 🧠 AI/ML COMPONENTS
│   ├── ml/
│   │   ├── rl_agent.py                   # Reinforcement learning agents
│   │   ├── trading_env.py                # RL trading environment
│   │   ├── adaptive_updater.py           # Online learning manager
│   │   ├── models.py                     # ML model ensemble
│   │   └── position_sizing.py            # Smart position sizing
│   │
├── 💼 PORTFOLIO MANAGEMENT
│   ├── portfolio/
│   │   └── portfolio_manager.py          # Advanced portfolio management
│   │
├── 📊 MONITORING & ANALYTICS
│   ├── monitoring/
│   │   └── performance_tracker.py        # Performance tracking & analytics
│   │
├── 🎯 SYMBOL SELECTION
│   ├── symbol_scanner.py                 # Dynamic symbol scanning
│   │
├── 🔧 CONFIGURATION
│   ├── config/
│   │   ├── autonomous_config.py          # Unified configuration system
│   │   └── config.py                     # Base configuration
│   │
├── 🧪 TESTING & VALIDATION
│   ├── tests/
│   │   └── mocks/                        # Mock trading components
│   ├── comprehensive_system_test.py      # Full system validation
│   ├── run_final_validation.py           # Deployment readiness check
│   └── test_autonomous_integration.py    # Integration testing
│   │
├── 🚀 TRAINING & DEPLOYMENT
│   ├── train_rl_agent.py                 # RL training pipeline
│   ├── start_paper_trading.py            # Paper/live trading launcher
│   └── validate_config.py                # Configuration validation
│   │
└── 📚 DOCUMENTATION
    ├── docs/                             # Comprehensive documentation
    └── README.md                         # This file
```

## 🎮 Usage Examples

### Paper Trading (Risk-Free Testing)
```bash
# Start paper trading with $10,000 virtual balance
python start_paper_trading.py --balance 10000 --verbose

# Paper trading with custom settings
python start_paper_trading.py --balance 5000 --max-positions 2 --min-confidence 0.75
```

### Live Trading (Real Money)
```bash
# Conservative live trading with $100
python start_paper_trading.py --live --balance 100

# Live trading with custom risk settings
python start_paper_trading.py --live --balance 500 --max-positions 1 --cycle-delay 60
```

### RL Training
```bash
# Train reinforcement learning agent
python train_rl_agent.py --timesteps 100000 --model-type PPO

# Train with custom configuration
python train_rl_agent.py --config configs/rl_training_config.json
```

### System Validation
```bash
# Run comprehensive system tests
python run_final_validation.py

# Validate configuration
python validate_config.py
```

## ⚙️ Configuration

The system uses a unified configuration management system located in `config/autonomous_config.py`.

### Key Configuration Sections:

- **Trading Config**: Balance, positions, confidence thresholds
- **Risk Config**: Stop losses, position limits, leverage controls
- **ML Config**: Model weights, ensemble settings, learning parameters
- **RL Config**: Reinforcement learning hyperparameters
- **Scanner Config**: Symbol selection and scanning parameters
- **Monitoring Config**: Performance tracking and alerting

### Example Configuration:
```yaml
trading:
  initial_balance: 10000.0
  max_positions: 3
  min_confidence: 0.65
  use_rl: false

risk:
  max_daily_loss: 0.05
  max_position_size: 0.30
  max_leverage: 10.0
  stop_loss_pct: 0.02

ml:
  performance_threshold: 0.55
  learning_rate: 0.01
  adaptation_window: 100
```

## 🛡️ Safety Features

### Multi-Layer Risk Management
- **Portfolio Risk Limits**: Maximum 20% portfolio risk
- **Position Size Limits**: Maximum 30% per position
- **Daily Loss Limits**: Automatic shutdown at 5% daily loss
- **Leverage Controls**: Dynamic leverage adjustment (1x-10x)
- **Emergency Stop**: Instant system shutdown capability

### Conservative Live Trading Defaults
- **Ultra-Conservative Settings**: Minimal position sizes for live trading
- **Real-time Monitoring**: Continuous performance and risk monitoring
- **Gradual Scaling**: Start small, scale based on proven performance
- **Circuit Breakers**: Automatic trading halt on anomalies

## 📈 Performance Monitoring

### Real-time Metrics
- **Trade Performance**: Win rate, profit/loss, Sharpe ratio
- **Risk Metrics**: Drawdown, volatility, risk-adjusted returns
- **Model Performance**: Individual model accuracy and contribution
- **System Health**: Component status, error rates, latency

### Analytics Dashboard
- **Trade Journal**: Detailed trade history and analysis
- **Performance Charts**: Visual performance tracking
- **Risk Reports**: Comprehensive risk analysis
- **Model Analytics**: ML model performance breakdown

## 🧪 Testing & Validation

The system includes comprehensive testing infrastructure:

- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **End-to-End Tests**: Full system workflow testing
- **Performance Tests**: System performance validation
- **Mock Trading**: Risk-free testing environment

### Validation Status: ✅ 100% PASS RATE ACHIEVED

All autonomous trading components have been validated:
- ✅ AutonomousTradeExecutor
- ✅ TradingEnvironment
- ✅ TradingRLAgent
- ✅ PerformanceTracker
- ✅ PortfolioManager
- ✅ OnlineLearningManager
- ✅ AutonomousController
- ✅ Configuration Management
- ✅ Symbol Scanner

## 🔬 Advanced Features

### Reinforcement Learning
- **PPO/SAC Agents**: State-of-the-art RL algorithms
- **Custom Trading Environment**: Realistic trading simulation
- **Continuous Learning**: Real-time strategy adaptation
- **Performance-Based Training**: Training based on actual trading results

### Online Learning
- **Adaptive Model Weights**: Dynamic ensemble weight adjustment
- **Performance Feedback**: Real-time model performance tracking
- **Concept Drift Detection**: Automatic adaptation to market changes
- **Ensemble Optimization**: Continuous model ensemble improvement

### Dynamic Symbol Selection
- **Real-time Scanning**: Continuous market opportunity scanning
- **Multi-Factor Analysis**: Spread, volatility, liquidity analysis
- **Adaptive Selection**: Dynamic symbol switching based on conditions
- **Performance-Based Ranking**: Symbol ranking based on trading success

### LLM Integration
- **Local Model Support**: LLaMA, Phi, and other local models
- **Market Analysis**: Comprehensive market sentiment analysis
- **Decision Reasoning**: Explainable AI decision-making
- **Risk Assessment**: AI-powered risk evaluation

## 🎯 Core Components

### Autonomous Controller (`core/autonomous_controller.py`)
The brain of the system that orchestrates all trading decisions:
- **Decision Engine**: Combines ML predictions with LLM analysis
- **Risk Management**: Multi-layer safety controls
- **Execution Coordination**: Smart order management and timing
- **Performance Monitoring**: Real-time system health tracking

### Trading Environment (`ml/trading_env.py`)
Realistic trading simulation environment for RL training:
- **Market Simulation**: Accurate market condition modeling
- **Action Space**: Comprehensive trading actions (buy, sell, hold, position sizing)
- **Reward Function**: Sophisticated reward calculation based on risk-adjusted returns
- **State Representation**: Rich market state encoding

### Portfolio Manager (`portfolio/portfolio_manager.py`)
Advanced portfolio and risk management:
- **Position Tracking**: Real-time position monitoring
- **Risk Calculation**: Dynamic risk assessment and limits
- **Portfolio Optimization**: Automatic rebalancing and optimization
- **Margin Management**: Intelligent leverage and margin control

### Performance Tracker (`monitoring/performance_tracker.py`)
Comprehensive performance analytics and monitoring:
- **Trade Analytics**: Detailed trade performance analysis
- **Risk Metrics**: Drawdown, Sharpe ratio, volatility tracking
- **Model Performance**: Individual ML model accuracy tracking
- **Real-time Alerts**: Performance-based alerting system

## 🚨 Important Disclaimers

⚠️ **TRADING RISKS**: Cryptocurrency trading involves substantial risk of loss. Past performance does not guarantee future results.

⚠️ **START SMALL**: Always begin with paper trading and minimal amounts for live trading.

⚠️ **MONITOR CLOSELY**: Continuously monitor system performance and market conditions.

⚠️ **REGULATORY COMPLIANCE**: Ensure compliance with local financial regulations.

⚠️ **NO GUARANTEES**: This system does not guarantee profits and may result in losses.

## 📞 Support & Documentation

- **Documentation**: Comprehensive docs in `/docs` directory
- **Configuration Guide**: See `docs/configuration.md`
- **API Reference**: See `docs/api/README.md`
- **Troubleshooting**: See `docs/troubleshooting.md`
- **Advanced Features**: See `docs/AUTONOMOUS_TRADING_GUIDE.md`

## 🏆 System Status

**🎉 DEPLOYMENT READY**: The Epinnox v6 Autonomous Trading System has achieved 100% validation and is ready for deployment with all autonomous components fully operational.

### Recent Achievements:
- ✅ **100% Test Pass Rate**: All components validated and working
- ✅ **Symbol Scanner Bug Fixed**: Update interval parameter now works correctly
- ✅ **RL Training Pipeline**: Complete reinforcement learning infrastructure
- ✅ **Online Learning**: Adaptive model weighting implemented
- ✅ **Unified Configuration**: Comprehensive configuration management system
- ✅ **Full Integration**: All autonomous components working seamlessly together

### Key Metrics:
- **Components**: 9 core autonomous components
- **ML Models**: 8 ensemble models + RL agents
- **Test Coverage**: 100% pass rate achieved
- **Configuration Options**: 50+ configurable parameters
- **Safety Features**: Multi-layer risk management
- **Performance Tracking**: Real-time analytics and monitoring

---

**Built with ❤️ for autonomous cryptocurrency trading**

*Epinnox v6 - Where AI meets autonomous trading excellence*
