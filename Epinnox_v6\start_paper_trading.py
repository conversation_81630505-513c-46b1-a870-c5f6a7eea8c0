#!/usr/bin/env python3
"""
Paper Trading CLI for EPINNOX v6
Run autonomous trading with real market data but no financial risk
"""

import argparse
import sys
import os
import asyncio
import signal
from datetime import datetime
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.mocks.mock_exchange import MockExchange
from execution.autonomous_executor import AutonomousTradeExecutor
from portfolio.portfolio_manager import PortfolioManager
from monitoring.performance_tracker import PerformanceTracker
from core.autonomous_controller import AutonomousController
from trading.ccxt_trading_engine import CCXTTradingEngine

# Import credentials for live trading
try:
    import credentials
    from credentials import validate_credentials, CONSERVATIVE_SETTINGS
except ImportError:
    credentials = None

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PaperTradingSession:
    """Paper trading session manager"""
    
    def __init__(self, config, live_mode=False):
        self.config = config
        self.live_mode = live_mode
        self.running = False
        self.mock_exchange = None
        self.live_exchange = None
        self.controller = None
        
    async def initialize(self):
        """Initialize trading components (paper or live)"""
        if self.live_mode:
            logger.info("🚀 Initializing LIVE trading session...")

            # Validate credentials for live trading
            if not credentials or not validate_credentials():
                raise Exception("Invalid credentials - cannot start live trading")

            # Create live exchange connection
            self.live_exchange = CCXTTradingEngine('htx', demo_mode=False)
            if not self.live_exchange.initialize_exchange():
                raise Exception("Failed to connect to HTX exchange in live mode")

            logger.info("✅ HTX exchange connected in LIVE MODE")

            # Use conservative settings for live trading
            live_config = {
                'initial_balance': min(self.config['initial_balance'], CONSERVATIVE_SETTINGS['initial_balance']),
                'max_positions': 1,  # Conservative: only 1 position
                'min_confidence': max(self.config['min_confidence'], CONSERVATIVE_SETTINGS['minimum_confidence']),
                'cycle_delay': max(self.config['cycle_delay'], 60),  # Minimum 60 seconds
                'use_rl': False,
                'base_delay': max(self.config['cycle_delay'], 60)
            }
            self.config.update(live_config)

            logger.info(f"✅ Live trading configured with balance: ${self.config['initial_balance']:.2f}")

        else:
            logger.info("🏗️ Initializing paper trading session...")

            # Create mock exchange with real-time price simulation
            self.mock_exchange = MockExchange(initial_balance=self.config['initial_balance'])

        # Create autonomous controller with appropriate exchange
        if self.live_mode:
            self.controller = AutonomousController(self.live_exchange.exchange, self.config)
        else:
            self.controller = AutonomousController(self.mock_exchange, self.config)

        await self.controller.initialize()

        if self.live_mode:
            logger.info("✅ LIVE trading session initialized")
        else:
            logger.info("✅ Paper trading session initialized")
    
    async def run_session(self, duration_minutes=None):
        """Run trading session (paper or live)"""
        if self.live_mode:
            logger.info("🚀 Starting LIVE trading session...")
            logger.info("⚠️  WARNING: This will trade with REAL MONEY!")
        else:
            logger.info("🚀 Starting paper trading session...")

        logger.info(f"Initial Balance: ${self.config['initial_balance']:,.2f}")
        logger.info(f"Max Positions: {self.config['max_positions']}")
        logger.info(f"Min Confidence: {self.config['min_confidence']:.1%}")

        if self.live_mode:
            logger.info("🛡️ SAFETY: Emergency stop with Ctrl+C")
        
        self.running = True
        start_time = datetime.now()
        cycle_count = 0
        
        try:
            while self.running:
                cycle_start = datetime.now()
                
                # Check duration limit
                if duration_minutes:
                    elapsed = (datetime.now() - start_time).total_seconds() / 60
                    if elapsed >= duration_minutes:
                        logger.info(f"⏰ Session duration limit reached ({duration_minutes} minutes)")
                        break
                
                # Simulate market data updates
                self.mock_exchange._update_prices()
                
                # Make trading decision (simplified for paper trading)
                decision_data = await self.simulate_trading_decision()
                
                # Execute decision through controller
                if decision_data['decision'] != 'WAIT':
                    execution_result = await self.controller.execute_trade(decision_data)
                    
                    if execution_result['status'] == 'FILLED':
                        logger.info(f"📈 Trade executed: {execution_result['side'].upper()} "
                                  f"{execution_result['amount']:.4f} {execution_result['symbol']} "
                                  f"@ ${execution_result['price']:.2f}")
                
                # Update portfolio with current prices
                if self.controller.portfolio_manager:
                    current_prices = {
                        symbol: self.mock_exchange.get_market_price(symbol)
                        for symbol in ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
                    }
                    await self.controller.portfolio_manager.update_positions(current_prices)
                
                cycle_count += 1
                
                # Display status every 10 cycles
                if cycle_count % 10 == 0:
                    await self.display_status(cycle_count)
                
                # Sleep between cycles
                await asyncio.sleep(self.config.get('cycle_delay', 30))
                
        except KeyboardInterrupt:
            logger.info("🛑 Paper trading session interrupted by user")
        except Exception as e:
            logger.error(f"❌ Error in paper trading session: {e}")
        finally:
            self.running = False
            await self.finalize_session(start_time, cycle_count)
    
    async def simulate_trading_decision(self):
        """Simulate trading decision for paper trading"""
        # Simple momentum-based decision for demonstration
        import random
        
        symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        selected_symbol = random.choice(symbols)
        
        # Simulate price analysis
        current_price = self.mock_exchange.get_market_price(selected_symbol)
        
        # Random decision with some logic
        decision_type = random.choices(
            ['LONG', 'SHORT', 'WAIT'],
            weights=[0.3, 0.3, 0.4]  # 40% wait, 30% each direction
        )[0]
        
        confidence = random.uniform(50, 95) if decision_type != 'WAIT' else 30
        
        return {
            'decision': decision_type,
            'confidence': confidence,
            'selected_symbol': selected_symbol,
            'leverage_position_sizing': {
                'position_units': 0.1,
                'position_usd': current_price * 0.1,
                'effective_leverage': random.uniform(1.0, 3.0),
                'stop_loss_price': current_price * 0.95 if decision_type == 'LONG' else current_price * 1.05,
                'take_profit_price': current_price * 1.05 if decision_type == 'LONG' else current_price * 0.95
            }
        }
    
    async def display_status(self, cycle_count):
        """Display current session status"""
        if not self.controller.portfolio_manager:
            return
        
        portfolio_summary = self.controller.portfolio_manager.get_portfolio_summary()
        
        print(f"\n{'='*60}")
        print(f"📊 PAPER TRADING STATUS - Cycle #{cycle_count}")
        print(f"{'='*60}")
        print(f"Current Balance:    ${portfolio_summary['balance']:,.2f}")
        print(f"Total Value:        ${portfolio_summary['total_value']:,.2f}")
        print(f"Unrealized P&L:     ${portfolio_summary['unrealized_pnl']:,.2f}")
        print(f"Total Return:       {portfolio_summary['total_return']:.2%}")
        print(f"Open Positions:     {portfolio_summary['open_positions']}")
        print(f"Portfolio Risk:     {portfolio_summary['portfolio_risk']:.1%}")
        
        if portfolio_summary['positions']:
            print(f"\n📈 Open Positions:")
            for symbol, pos in portfolio_summary['positions'].items():
                pnl_color = "🟢" if pos['unrealized_pnl'] >= 0 else "🔴"
                print(f"  {pnl_color} {symbol}: {pos['side'].upper()} "
                      f"{pos['size']:.4f} @ ${pos['entry_price']:.2f} "
                      f"(Current: ${pos['current_price']:.2f}, "
                      f"P&L: ${pos['unrealized_pnl']:.2f})")
        
        print(f"{'='*60}\n")
    
    async def finalize_session(self, start_time, cycle_count):
        """Finalize paper trading session"""
        duration = datetime.now() - start_time
        
        # Close all positions
        if self.controller.portfolio_manager:
            for symbol in list(self.controller.portfolio_manager.positions.keys()):
                current_price = self.mock_exchange.get_market_price(symbol)
                await self.controller.portfolio_manager.close_position(symbol, current_price)
        
        # Generate final report
        final_summary = self.controller.portfolio_manager.get_portfolio_summary() if self.controller.portfolio_manager else {}
        
        print(f"\n{'='*60}")
        print(f"📋 PAPER TRADING SESSION SUMMARY")
        print(f"{'='*60}")
        print(f"Session Duration:   {duration}")
        print(f"Total Cycles:       {cycle_count}")
        print(f"Initial Balance:    ${self.config['initial_balance']:,.2f}")
        print(f"Final Balance:      ${final_summary.get('balance', 0):,.2f}")
        print(f"Total Return:       {final_summary.get('total_return', 0):.2%}")
        print(f"Trades Executed:    {len(self.controller.portfolio_manager.trade_history) if self.controller.portfolio_manager else 0}")
        
        if self.controller.portfolio_manager and self.controller.portfolio_manager.trade_history:
            winning_trades = len([t for t in self.controller.portfolio_manager.trade_history if t['pnl_usd'] > 0])
            total_trades = len(self.controller.portfolio_manager.trade_history)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            print(f"Win Rate:           {win_rate:.1%}")
        
        print(f"{'='*60}")
        
        # Save session report
        await self.save_session_report(start_time, duration, cycle_count, final_summary)
    
    async def save_session_report(self, start_time, duration, cycle_count, final_summary):
        """Save paper trading session report"""
        import json
        
        report = {
            'session_info': {
                'start_time': start_time.isoformat(),
                'duration_seconds': duration.total_seconds(),
                'cycles_completed': cycle_count,
                'config': self.config
            },
            'performance': final_summary,
            'trades': [
                {
                    'symbol': trade['symbol'],
                    'side': trade['side'],
                    'entry_price': trade['entry_price'],
                    'exit_price': trade.get('exit_price'),
                    'pnl_usd': trade['pnl_usd'],
                    'duration_minutes': trade.get('duration_minutes'),
                    'timestamp': trade['timestamp'].isoformat() if hasattr(trade.get('timestamp'), 'isoformat') else str(trade.get('timestamp'))
                }
                for trade in (self.controller.portfolio_manager.trade_history if self.controller.portfolio_manager else [])
            ]
        }
        
        filename = f"paper_trading_session_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Session report saved to {filename}")
    
    def stop(self):
        """Stop the paper trading session"""
        self.running = False

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Paper Trading')
    
    parser.add_argument('--balance', type=float, default=10000.0,
                       help='Initial balance (default: 10000)')
    parser.add_argument('--duration', type=int, 
                       help='Session duration in minutes')
    parser.add_argument('--max-positions', type=int, default=3,
                       help='Maximum concurrent positions (default: 3)')
    parser.add_argument('--min-confidence', type=float, default=0.65,
                       help='Minimum confidence threshold (default: 0.65)')
    parser.add_argument('--cycle-delay', type=int, default=30,
                       help='Delay between cycles in seconds (default: 30)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--live', action='store_true',
                       help='Enable live trading mode (uses real money)')

    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create configuration
    config = {
        'initial_balance': args.balance,
        'max_positions': args.max_positions,
        'min_confidence': args.min_confidence,
        'cycle_delay': args.cycle_delay,
        'use_rl': False,
        'base_delay': args.cycle_delay
    }
    
    # Create and run trading session (paper or live)
    session = PaperTradingSession(config, live_mode=args.live)
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info("🛑 Received shutdown signal")
        session.stop()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(run_paper_trading_session(session, args.duration))
    except KeyboardInterrupt:
        logger.info("🛑 Paper trading interrupted")
    except Exception as e:
        logger.error(f"❌ Paper trading failed: {e}")
        return 1
    
    return 0

async def run_paper_trading_session(session, duration):
    """Run the paper trading session"""
    await session.initialize()
    await session.run_session(duration)

if __name__ == "__main__":
    sys.exit(main())
