2025-06-29 11:11:21,060 - main - INFO - Epinnox v6 starting up...
2025-06-29 11:11:21,081 - core.performance_monitor - INFO - Performance monitoring started
2025-06-29 11:11:21,081 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-29 11:11:21,082 - main - INFO - Performance monitoring initialized
2025-06-29 11:11:21,093 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-29 11:11:21,094 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-29 11:11:21,095 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-29 11:11:32,566 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-29 11:11:33,809 - websocket - INFO - Websocket connected
2025-06-29 11:11:37,011 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-06-29 11:11:37,022 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-29 11:11:37,023 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-29 11:11:37,023 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-29 11:11:37,024 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-29 11:11:37,031 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-29 11:11:39,204 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-29 11:11:39,205 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-29 11:11:39,206 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-29 11:11:39,206 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-06-29 11:11:39,207 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-06-29 11:11:39,207 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-29 11:11:39,208 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-29 11:11:39,208 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-29 11:11:39,214 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-29 11:11:39,215 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-29 11:11:39,235 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-29 11:11:39,236 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-29 11:11:39,236 - storage.session_manager - INFO - Session Manager initialized
2025-06-29 11:11:39,243 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250629_111139_7923a883
2025-06-29 11:11:39,245 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250629_111139_7923a883
2025-06-29 11:11:39,404 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-29 11:11:39,405 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-29 11:14:11,004 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-29 11:14:11,005 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-29 11:14:11,007 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-29 11:14:11,008 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-29 11:14:11,010 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-29 11:14:11,010 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-29 11:14:11,011 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-29 11:14:21,761 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 673 chars
2025-06-29 11:14:21,762 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-29 11:14:21,762 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 164, Total: 286
2025-06-29 11:14:21,764 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES EARLY WHILE SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF PROLONGED BEARISH TRENDS.', 'ACTION': 'ENTER_NOW'}
2025-06-29 11:14:21,765 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-29 11:14:21,820 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-29 11:14:21,821 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-29 11:14:21,882 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-29 11:14:21,882 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-29 11:14:21,944 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-29 11:14:21,945 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 10.94s - 3 prompts executed sequentially
2025-06-29 11:15:24,392 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-29 11:15:24,393 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-29 11:15:24,393 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-29 11:15:24,394 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-29 11:15:24,394 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-29 11:15:24,395 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-29 11:15:24,395 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-29 11:15:29,207 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 728 chars
2025-06-29 11:15:29,209 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that similar patterns have led to significa...
2025-06-29 11:15:29,210 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 180, Total: 302
2025-06-29 11:15:29,211 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SHORT TIME FRAMES. BY TAKING A SHORT POSITION NOW, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE SETTING STOP-LOSS ORDERS AT 20% BELOW THE CURRENT MARKET LEVEL TO MANAGE RISK EFFECTIVELY. THE TAKE-PROFIT IS SET AT 10%, ANTICIPATING THAT IF OUR ANALYSIS PROVES CORRECT AND SENTIMENT SHIFTS POSITIVELY BEFORE REACHING THE TARGET PRICE, THERE COULD BE A QUICK RECOVERY IN VALUE FOR US TO EXIT WITH PROFIT.', 'ACTION': 'ENTER_NOW'}
2025-06-29 11:15:29,213 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-29 11:15:29,266 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-29 11:15:29,268 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-29 11:15:29,328 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-29 11:15:29,329 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-29 11:15:29,390 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-29 11:15:29,390 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.00s - 3 prompts executed sequentially
2025-06-29 11:16:31,158 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-29 11:16:31,159 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-29 11:16:31,160 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-29 11:16:31,161 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-29 11:16:31,161 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-29 11:16:31,162 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-29 11:16:31,162 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-29 11:16:35,831 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 734 chars
2025-06-29 11:16:35,832 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may begin to decline sharply, as indi...
2025-06-29 11:16:35,833 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 176, Total: 298
2025-06-29 11:16:35,834 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SUGGEST THAT WE ARE APPROACHING AN INFLECTION POINT WHERE PRICES MAY BEGIN TO DECLINE SHARPLY, AS INDICATED BY SIMILAR PAST TRENDS DURING PERIODS OF HIGH VOLATILITY AND SPECULATIVE TRADING ACTIVITY. GIVEN THESE FACTORS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET CORRECTS UPWARDS BRIEFLY BEFORE CONTINUING ITS DOWNWARD TRAJECTORY, WHILE SETTING STOP LOSS ORDERS TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED SURGE OR PROLONGED DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-29 11:16:35,835 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-29 11:16:35,902 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-29 11:16:35,902 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-29 11:16:35,965 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-29 11:16:35,966 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-29 11:16:36,027 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-29 11:16:36,027 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.87s - 3 prompts executed sequentially
