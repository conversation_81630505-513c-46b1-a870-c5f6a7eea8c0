{"tests/test_gui_components.py": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_position_display_integration": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_portfolio_exposure_warning_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_llm_orchestrator_results_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_leverage_mismatch_error_display": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_emergency_stop_accessibility": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_real_time_data_integration": true, "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_account_balance_display": true, "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_websocket_disconnection_handling": true, "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_portfolio_overexposure_prevention": true}